import request from '@/utils/requestUtil'

export function fetchOrderList(params) {
	return request({
		method: 'GET',
		url: '/vehicleInspectionOrder/list',
		params: params
	})
}

export function payOrderSuccess(data) {
	return request({
		method: 'POST',
		url: '/order/paySuccess',
		header: {
			'content-type': 'application/x-www-form-urlencoded;charset=utf-8'
		},
		data: data
	})
}

export function fetchOrderDetail(orderId) {
	return request({
		method: 'GET',
		url: `/vehicleInspectionOrder/detail/${orderId}`
	})
}

export function confirmReceiveOrder(data) {
	return request({
		method: 'POST',
		url: '/order/confirmReceiveOrder',
		header: {
			'content-type': 'application/x-www-form-urlencoded;charset=utf-8'
		},
		data: data
	})
}

export function fetchAliapyStatus(params) {
	return request({
		method: 'GET',
		url: '/alipay/query',
		params: params
	})
}

export function fetchWxpayStatus(data) {
	return request({
		method: 'POST',
		url: '/wx/transactionQueryByOutTradeNo',
		data: data
	})
}

export function startPickUp(data) {
	return request({
		method: 'POST',
		url: '/vehicleInspectionOrder/startPickup',
		data: data
	})
}

export function pickUp(data) {
	return request({
		method: 'POST',
		url: '/vehicleInspectionOrder/pickedUp',
		data: data
	})
}

export function doStartInspection(data) {
	return request({
		method: 'POST',
		url: '/vehicleInspectionOrder/startInspection',
		data: data
	})
}

export function doFinishInspection(data) {
	return request({
		method: 'POST',
		url: '/vehicleInspectionOrder/finishInspection',
		data: data
	})
}

export function doReturnVehicle(data) {
	return request({
		method: 'POST',
		url: '/vehicleInspectionOrder/returnVehicle',
		data: data
	})
}

export function doCancelAppointment(data) {
	return request({
		method: 'POST',
		url: '/vehicleInspectionOrder/cancelOrder',
		data: data
	})
}

export function doChangeAppointmentTime(data) {
	return request({
		method: 'POST',
		url: '/vehicleInspectionOrder/changeAppointmentTime',
		data: data
	})
}

export function cancelUserOrder(data) {
	return request({
		method: 'POST',
		url: '/vehicleInspectionOrder/cancelOrder',
		data: data
	})
}

export function queryAppointmentSort(params) {
	return request({
		method: 'GET',
		url: '/appointment/querySort',
		params: params
	})
}

export function queryOrderNo(params) {
	return request({
		method: 'GET',
		url: '/vehicleInspectionOrder/queryOrderNo',
		params: params
	})
}

export function refund(params) {
	return request({
		method: 'POST',
		url: '/alipay/refund',
		params: params
	})
}

export function refundWx(data) {
    return request({
        method: 'POST',
        url: '/wx/refund',
        data
    })
}
