<template>
    <view class="payment-container">
      <!-- 顶部状态栏 -->
      <view class="status-bar">
        <view class="back-btn" @click="handleBack">
          <uni-icons type="left" size="20" color="#333"></uni-icons>
        </view>
        <text class="title">支付确认</text>
      </view>

      <!-- 订单信息卡片 -->
      <view class="order-card">
        <view class="price-section">
          <text class="amount">￥{{ orderInfo.totalAmount }}</text>
          <text class="desc">{{ orderInfo.vehicleType === 'car' ? '汽车检测服务' : orderInfo.vehicleType === 'electric' ? '电动汽车检测服务' : '摩托车检测服务' }}</text>
        </view>

        <!-- 订单详情 -->
        <view class="order-details">
          <view class="detail-item">
            <text class="label">订单编号</text>
            <text class="value">{{ orderInfo.orderNo }}</text>
          </view>
          <view class="detail-item">
            <text class="label">预约时间</text>
            <text class="value">{{ orderInfo.appointmentTime }}</text>
          </view>
          <view class="detail-item">
            <text class="label">车牌号</text>
            <text class="value">{{ orderInfo.plateNumber }}</text>
          </view>
          <view v-if="orderInfo.isHomePickup" class="detail-item">
            <text class="label">取车地点</text>
            <text class="value">{{ orderInfo.pickupAddress }}</text>
          </view>
          <view class="detail-item">
            <text class="label">检车费</text>
            <text class="value">￥{{ orderInfo.payAmount }}</text>
          </view>
          <view v-if="orderInfo.isHomePickup" class="detail-item">
            <text class="label">取车费</text>
            <text class="value">￥{{ orderInfo.pickAmount }}</text>
        </view>
        </view>
      </view>

      <!-- 支付方式选择 -->
      <view class="payment-methods">
        <text class="section-title">选择支付方式</text>
        <view
          class="method-item"
          v-for="method in paymentMethods"
          :key="method.id"
          @click="selectPayment(method)"
          :class="{ active: selectedMethod === method.id }"
        >
          <view class="method-info">
            <image :src="method.icon" style="color: #3ab54a" class="method-icon"></image>
            <text class="method-name">{{ method.name }}</text>
            <text v-if="!method.disabled" class="coming-soon">(可到现场后再点击支付！)</text>
            <text v-if="method.disabled" class="coming-soon">(测试中，待上线，可现场支付！)</text>
          </view>
          <uni-icons :type="selectedMethod === method.id ? 'checkbox-filled' : 'circle'"
                    size="20"
                    :color="selectedMethod === method.id ? '#2349A4' : '#999'">
          </uni-icons>
        </view>
      </view>

      <!-- 优惠码输入区域 -->
      <view class="coupon-section">
        <text class="section-title">优惠码</text>
        <view class="coupon-input-container">
          <input
            class="coupon-input"
            v-model="couponCode"
            placeholder="请输入优惠码"
            :disabled="couponApplied"
            maxlength="20"
          />
          <button
            class="verify-btn"
            @click="verifyCoupon"
            :disabled="!couponCode || couponVerifying || couponApplied"
            :class="{ 'applied': couponApplied }"
          >
            {{ couponApplied ? '已应用' : (couponVerifying ? '验证中...' : '验证') }}
          </button>
        </view>
        <view v-if="couponError" class="coupon-error">
          <text>{{ couponError }}</text>
        </view>
        <view v-if="couponApplied && discountAmount > 0" class="coupon-success">
          <text>优惠码已生效，减免￥{{ discountAmount }}</text>
          <button class="reset-coupon-btn" @click="resetCouponCode">更换</button>
        </view>
      </view>

      <!-- 支付按钮 -->
      <view class="bottom-bar">
        <view class="price-info">
          <view v-if="discountAmount > 0" class="price-detail">
            <text class="original-amount">原价：￥{{ orderInfo.totalAmount }}</text>
            <text class="discount-amount">优惠：-￥{{ discountAmount }}</text>
          </view>
          <view class="final-amount">
            <text class="label">实付金额</text>
            <text class="amount">￥{{ finalAmount }}</text>
          </view>
        </view>
        <button class="pay-button" @click="handlePayment">立即支付</button>
      </view>
    </view>
  </template>

  <script>
  import uniIcons from "@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue"
import {alipayWebPay} from "@/api/payment";
  import {createWechatOrder} from "@/api/wechatPay";
  import {validateCoupon} from "@/api/coupon";
  import {
    mapState, mapMutations
  } from 'vuex';
  import {getWxOpenid, memberInfo, updateWxOpenid} from "@/api/member";
  export default {
    components: {
      uniIcons
    },
    data() {
      return {
        orderInfo: {
          vehicleType: '',
          orderNo: '',
          tradeNo: '',
          appointmentTime: '',
          plateNumber: '',
          pickupAddress: '',
          isHomePickup: false,
          pickAmount: 0,
          payAmount: 0,
          totalAmount: 0
        },
        paymentMethods: [
           {
            id: 'wxpay',
            name: '微信支付',
            icon: '/static/wxpay.min.jpg',
          },
          {
            id: 'alipay',
            name: '支付宝支付',
            icon: '/static/alipay.png'
          }
        ],
        selectedMethod: 'wxpay',
        // 优惠码相关数据
        couponCode: '',           // 用户输入的优惠码
        couponVerifying: false,   // 是否正在验证优惠码
        couponApplied: false,     // 优惠码是否已应用
        couponError: '',          // 优惠码错误信息
        discountAmount: 0,        // 减免金额
        couponInfo: null          // 优惠码详细信息
      }
    },
    onLoad(options) {
      console.log('页面参数：', options);  // 添加调试日志
      // 获取上一页传递的订单信息
      if (options.orderInfo) {
        this.orderInfo = JSON.parse(decodeURIComponent(options.orderInfo))
        console.log('订单信息：', this.orderInfo);  // 添加调试日志
      }
      // 禁止页面滚动
      this.disablePageScroll();
    },
    async onShow() {
      // 页面显示时也禁止滚动
      this.disablePageScroll();

      // 每次显示页面时，获取最新的用户信息
      try {
        const userInfoResponse = await memberInfo();
        this.login(userInfoResponse.data);
      } catch (error) {
        console.error('获取用户信息失败:', error);
        // 如果获取用户信息失败，可能是token过期等问题，这里不显示错误提示
        // 因为用户可能只是想查看页面，不需要打断用户体验
      }
    },
    onUnload() {
      // 页面卸载时恢复滚动
      this.enablePageScroll();
    },
    computed: {
      ...mapState(['userInfo']),
      // 计算最终支付金额
      finalAmount() {
        const originalAmount = parseFloat(this.orderInfo.totalAmount || 0);
        const discount = parseFloat(this.discountAmount || 0);
        const final = Math.max(0, originalAmount - discount);
        return final.toFixed(2);
      }
    },
    methods: {
      ...mapMutations(['logout', 'login']),
      selectPayment(method) {
        if (!method.disabled) {
          this.selectedMethod = method.id;
        }
      },
      async handlePayment() {

        // 根据选择的支付方式调用不同的支付接口
        if (this.selectedMethod === 'alipay') {
          try {
            let copyPay = 0;
            // #ifdef MP-WEIXIN
            // 在微信小程序中
            copyPay = 1;
            // #endif
            const res = await alipayWebPay({
              outTradeNo: this.orderInfo.tradeNo,
              totalAmount: this.finalAmount, // 使用最终金额（已减去优惠）
              subject: this.orderInfo.vehicleType === 'car' ? '汽车检测服务' : this.orderInfo.vehicleType === 'electric' ? '电动汽车检测服务' : '摩托车检测服务',
              couponCode: this.couponApplied ? this.couponCode : null, // 传递优惠码信息
              discountAmount: this.discountAmount, // 传递减免金额
              copyPay: copyPay
            });

            // 确保响应数据是字符串
            const htmlContent = typeof res.data === 'string' ? res.data : JSON.stringify(res.data);
            console.log('支付宝返回的HTML内容：', htmlContent);  // 添加调试日志

            // 存储数据前先清除旧数据
            uni.removeStorageSync('alipayHtml');
            // 存储支付宝返回的HTML内容
            uni.setStorageSync('alipayHtml', htmlContent);

            // #ifdef MP-WEIXIN
            // 在微信小程序中，跳转到引导页面
            uni.hideLoading();
            uni.navigateTo({
              url: '/pages/public/open-in-browser-tip'
            });
            // #endif

          } catch (error) {
            console.error('支付请求详细错误：', error);
            uni.hideLoading();

            let errorMsg = '支付失败';
            if (error.response) {
              errorMsg = error.response.data?.message || '服务器响应错误';
            } else if (error.request) {
              errorMsg = '网络请求失败，请检查网络连接';
            } else if (error.message) {
              errorMsg = error.message;
            }

            uni.showToast({
              title: errorMsg,
              icon: 'none',
              duration: 2000
            });
          } finally {
            uni.hideLoading();
          }
        } else if (this.selectedMethod === 'wxpay') {
          const tradeNo = this.orderInfo.tradeNo;
          if (!this.userInfo.wechatOpenid) {
            uni.showModal({
              title: '提示',
              content: '请使用微信授权登录后再支付',
              success: (res) => {
                if (res.confirm) {
                  // 调用微信登录接口获取 code
                  uni.login({
                    provider: 'weixin', //使用微信登录
                    success: async (loginRes) => {
                      console.log(loginRes);
                      if (loginRes) {
                        try {
                          // 调用后端接口完成微信登录
                          const wxResponse = await getWxOpenid({code: loginRes.code});
                          if (wxResponse.data) {
                            const updateResponse = await updateWxOpenid({openid: wxResponse.data});
                            console.log(updateResponse);
                            if (updateResponse.code === 200) {
                              const userInfoResponse = await memberInfo()
                              this.login(userInfoResponse.data);
                              uni.showToast({
                                title: '绑定成功',
                                icon: 'success',
                                duration: 2000
                              });

                            } else {
                              uni.showToast({
                                title: updateResponse.data.message || '绑定失败',
                                icon: 'none',
                                duration: 2000
                              });
                            }
                          } else {
                            uni.showToast({
                              title: '获取微信openid失败',
                              icon: 'none',
                              duration: 2000
                            });
                          }
                        } catch (error) {
                          console.error('微信绑定错误:', error);
                          uni.showToast({
                            title: '绑定失败，请重试',
                            icon: 'none',
                            duration: 2000
                          });
                        }
                      } else {
                        uni.showToast({
                          title: '获取微信登录凭证失败',
                          icon: 'none',
                          duration: 2000
                        });
                      }
                    },
                    fail: (error) => {
                      console.error('微信登录失败:', error);
                      uni.showToast({
                        title: '微信登录失败',
                        icon: 'none',
                        duration: 2000
                      });
                    }
                  });
                } else if (res.cancel) {
                  console.log('用户点击取消');
                }
              }
            });
          } else {
            uni.showLoading({
              title: '支付处理中'
            });
            createWechatOrder({
                  'description': this.orderInfo.vehicleType === 'car' ? '汽车检测服务' : this.orderInfo.vehicleType === 'electric' ? '电动汽车检测服务' : '摩托车检测服务',
                  'outTradeNo': this.orderInfo.tradeNo,
                  'amount': {
                    'total': Math.round(this.finalAmount * 100), // 使用最终金额（已减去优惠）
                    'currency': 'CNY'
                  },
                  'notifyUrl': 'https://ts.shengdaochejian.top/portal-backend/wx/notify/order',
                  'payer': {
                    'openid': this.userInfo.wechatOpenid
                  },
                  'attach': JSON.stringify({
                    couponCode: this.couponApplied ? this.couponCode : null,
                    discountAmount: this.discountAmount
                  }),

                }
            ).then(res => {
              console.log('微信支付订单创建成功', res);
              var resp = JSON.parse(res.data);
              console.log('微信支付订单创建成功', resp);
              // 调起微信支付
              uni.requestPayment({
                provider: 'wxpay', // 服务提提供商
                timeStamp: resp.timeStamp, // 时间戳
                nonceStr: resp.nonceStr, // 随机字符串
                package: resp.packageValue,
                signType: resp.signType, // 签名算法
                paySign: resp.paySign, // 签名
                success: function (res) {
                  console.log('支付成功',res);
                  const params = {
                    'out_trade_no': tradeNo,
                    'payMethod': 'wxpay'
                  }
                  // 业务逻辑。。。
                  uni.reLaunch({
                    url: '/pages/orders/paySuccess?' + Object.keys(params).map(key => `${key}=${params[key]}`).join('&')
                  });
                },
                fail: function (err) {
                  console.log('支付失败',err);
                }
              });
              uni.hideLoading();
            });
          }

        }
      },
      handleBack() {
        uni.showModal({
          title: '提示',
          content: '确定要放弃支付吗？',
          success: (res) => {
            if (res.confirm) {
              uni.reLaunch({
                url: '/pages/orders/order'
              });
            }
          }
        });
      },
      // 禁止页面滚动
      disablePageScroll() {
        // #ifdef H5
        // H5环境下禁止body滚动
        document.body.style.overflow = 'hidden';
        document.documentElement.style.overflow = 'hidden';
        // #endif

        // uni-app API禁止页面滚动
        uni.pageScrollTo({
          scrollTop: 0,
          duration: 0
        });
      },
      // 恢复页面滚动
      enablePageScroll() {
        // #ifdef H5
        // H5环境下恢复body滚动
        document.body.style.overflow = 'auto';
        document.documentElement.style.overflow = 'auto';
        // #endif
      },

      // 验证优惠码
      async verifyCoupon() {
        if (!this.couponCode.trim()) {
          this.couponError = '请输入优惠码';
          return;
        }

        this.couponVerifying = true;
        this.couponError = '';

        try {
          // 调用API验证优惠码
          const response = await this.validateCouponCode(this.couponCode, this.orderInfo.totalAmount);

          if (response.code === 200) {
            // 优惠码有效
            this.couponApplied = true;
            this.discountAmount = response.data.discountAmount;
            this.couponInfo = response.data;
            this.couponError = '';

            uni.showToast({
              title: `优惠码生效，减免￥${this.discountAmount}`,
              icon: 'success'
            });
          } else {
            // 优惠码无效
            this.couponError = response.message || '优惠码无效';
            this.resetCoupon();
          }
        } catch (error) {
          console.error('验证优惠码失败:', error);
          this.couponError = '验证失败，请重试';
          this.resetCoupon();
        } finally {
          this.couponVerifying = false;
        }
      },

      // 重置优惠码状态
      resetCoupon() {
        this.couponApplied = false;
        this.discountAmount = 0;
        this.couponInfo = null;
      },

      // 重置优惠码（用户主动更换）
      resetCouponCode() {
        uni.showModal({
          title: '提示',
          content: '确定要更换优惠码吗？',
          success: (res) => {
            if (res.confirm) {
              this.couponCode = '';
              this.couponError = '';
              this.resetCoupon();
              uni.showToast({
                title: '已重置，请重新输入优惠码',
                icon: 'none'
              });
            }
          }
        });
      },

      // 调用后端API验证优惠码
      async validateCouponCode(couponCode, orderAmount) {
        try {
          const response = await validateCoupon({
            couponCode: couponCode,
            totalAmount: orderAmount
          });
          return response;
        } catch (error) {
          console.error('优惠码验证API调用失败:', error);
          throw error;
        }
      }
    }
  }
  </script>

  <style lang="scss">
  .payment-container {
    height: 100vh; /* 修改为100vh，避免超出屏幕高度 */
    background-color: #f5f5f5;
    padding-bottom: 120rpx; /* 恢复正常的底部内边距 */
    overflow: hidden; /* 禁止滚动 */
    position: fixed; /* 固定定位，防止滚动 */
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;

    .status-bar {
      height: 200rpx;
      position: relative;
      background-color: #fff;
      padding: 20rpx 30rpx;
      display: flex;
      align-items: center;

      .back-btn {
        position: absolute;
        left: 30rpx;
        top: 50%;
        padding: 20rpx;
        z-index: 1;

        &:active {
          opacity: 0.8;
        }
      }

      .title {
        padding-top: 70rpx;
        flex: 1;
        text-align: center;
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }
    }

    .order-card {
      margin: 20rpx;
      background-color: #fff;
      border-radius: 16rpx;
      padding: 30rpx;
      max-height: 60vh; /* 限制最大高度 */
      overflow-y: auto; /* 内容超出时显示滚动条 */

      .price-section {
        text-align: center;
        padding-bottom: 30rpx;
        border-bottom: 1px solid #eee;

        .amount {
          font-size: 60rpx;
          font-weight: bold;
          color: #333;
        }

        .desc {
          font-size: 28rpx;
          color: #666;
          margin-top: 10rpx;
          display: block;
        }
      }

      .order-details {
        margin-top: 30rpx;

        .detail-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 20rpx;

          .label {
            color: #666;
            font-size: 28rpx;
          }

          .value {
            color: #333;
            font-size: 28rpx;
          }
        }
      }
    }

    /* 优惠码样式 */
    .coupon-section {
      margin: 20rpx;
      background-color: #fff;
      border-radius: 16rpx;
      padding: 20rpx; /* 减少内边距 */

      .section-title {
        font-size: 28rpx; /* 减少字体大小 */
        color: #333;
        margin-bottom: 15rpx; /* 减少下边距 */
        display: block;
      }
    }

    .payment-methods {
      margin: 20rpx;
      background-color: #fff;
      border-radius: 16rpx;
      padding: 30rpx;

      .section-title {
        font-size: 30rpx;
        color: #333;
        margin-bottom: 20rpx;
        display: block;
      }

      .method-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20rpx 0;
        border-bottom: 1px solid #eee;

        &:last-child {
          border-bottom: none;
        }

        &.active {
          .method-name {
            color: #2349A4;
          }
        }

        .method-info {
          display: flex;
          align-items: center;

          .method-icon {
            width: 48rpx;
            height: 48rpx;
            margin-right: 20rpx;
          }

          .method-name {
            font-size: 30rpx;
            color: #333;
          }
        }
      }
    }

    .bottom-bar {
      //#ifdef WEB
      height: 100upx;
      //#endif
      //#ifdef MP-WEIXIN
      height: 160upx;
      //#endif
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background-color: #ffffff;
      padding: 20rpx 30rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
      z-index: 100; /* 确保在优惠码区域上方 */

      .price-info {
        .label {
          font-size: 28rpx;
          color: #666;
          margin-right: 10rpx;
        }

        .amount {
          font-size: 36rpx;
          color: #C1141D;
          font-weight: bold;
        }
      }

      .pay-button {
        width: 240rpx;
        height: 80rpx;
        line-height: 80rpx;
        background-color: #2349A4;
        color: #ffffff;
        font-size: 32rpx;
        text-align: center;
        border-radius: 40rpx;
        border: none;
        margin: 0;
        padding: 0;
      }
    }
    .method-item {
      &.disabled {
        opacity: 0.6;
        pointer-events: none;
      }
      .coming-soon {
        font-size: 24rpx;
        color: #999;
        margin-left: 10rpx;
      }
    }

    /* 优惠码样式 */
    .coupon-input-container {
      display: flex;
      align-items: center;
      gap: 15rpx; /* 减少间距 */
      margin-bottom: 15rpx; /* 减少下边距 */
    }

    .coupon-input {
      flex: 1;
      height: 70rpx; /* 减少高度 */
      padding: 0 15rpx; /* 减少内边距 */
      border: 2rpx solid #e0e0e0;
      border-radius: 6rpx; /* 减少圆角 */
      font-size: 26rpx; /* 减少字体大小 */
      background-color: #fff;
    }

    .coupon-input:focus {
      border-color: #2349A4;
    }

    .coupon-input:disabled {
      background-color: #f5f5f5;
      color: #999;
    }

    .verify-btn {
      height: 70rpx; /* 与输入框高度一致 */
      padding: 0 15rpx; /* 减少内边距 */
      background-color: #52c41a;
      color: #fff;
      border: none;
      border-radius: 6rpx; /* 与输入框圆角一致 */
      font-size: 26rpx; /* 减少字体大小 */
      min-width: 120rpx; /* 减少最小宽度 */
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      line-height: 1;
    }

    .verify-btn:disabled {
      background-color: #ccc;
      color: #999;
    }

    .verify-btn.applied {
      background-color: #52c41a;
    }

    .coupon-error {
      color: #ff4d4f;
      font-size: 22rpx; /* 减少字体大小 */
      margin-top: 8rpx; /* 减少上边距 */
    }

    .coupon-success {
      color: #52c41a;
      font-size: 22rpx; /* 减少字体大小 */
      margin-top: 8rpx; /* 减少上边距 */
      font-weight: bold;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .reset-coupon-btn {
      height: 70rpx; /* 与验证按钮高度一致 */
      padding: 0 15rpx; /* 与验证按钮内边距一致 */
      background-color: #52c41a; /* 与验证按钮背景色一致 */
      color: #fff;
      border: none;
      border-radius: 6rpx; /* 与验证按钮圆角一致 */
      font-size: 26rpx; /* 与验证按钮字体大小一致 */
      min-width: 120rpx; /* 与验证按钮最小宽度一致 */
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      line-height: 1;
      margin-left: 10rpx;
    }

    /* 价格详情样式 */
    .price-detail {
      margin-bottom: 10rpx;
    }

    .original-amount, .discount-amount {
      font-size: 24rpx;
      color: #666;
      margin-right: 20rpx;
    }

    .discount-amount {
      color: #52c41a;
    }

    .final-amount {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }


  }
  </style>
