<template>
  <view class="container">
    <view class="header">
      <text class="title">图片组件测试页面</text>
    </view>
    
    <view class="test-section">
      <text class="section-title">测试图片列表</text>
      <upload-images
        :initialImages="testImages"
        :readonly="true"
        :length="testImages.length"
      />
    </view>
    
    <view class="debug-section">
      <text class="section-title">调试信息</text>
      <view class="debug-info">
        <text>平台: {{ systemInfo.platform }}</text>
        <text>网络类型: {{ networkType }}</text>
        <text>图片数量: {{ testImages.length }}</text>
      </view>
    </view>
    
    <view class="action-section">
      <button @click="refreshImages" class="refresh-btn">刷新图片</button>
      <button @click="testNetworkImages" class="test-btn">测试网络图片</button>
      <button @click="testLocalImages" class="test-btn">测试本地图片</button>
    </view>
  </view>
</template>

<script>
import uploadImages from '@/components/upload-images.vue';

export default {
  components: {
    uploadImages
  },
  data() {
    return {
      systemInfo: {},
      networkType: '',
      testImages: [
        // 测试不同类型的图片
        {
          src: 'https://img.picui.cn/free/2025/06/20/685519e2eb24f.jpeg',
          filePath: 'https://img.picui.cn/free/2025/06/20/685519e2eb24f.jpeg'
        },
        {
          src: '/static/carType.svg',
          filePath: '/static/carType.svg'
        },
        // 测试字符串格式
        'https://img.picui.cn/free/2025/06/20/68551abd65aa7.png'
      ]
    }
  },
  onLoad() {
    this.getSystemInfo();
    this.getNetworkType();
  },
  methods: {
    getSystemInfo() {
      this.systemInfo = uni.getSystemInfoSync();
      console.log('系统信息:', this.systemInfo);
    },
    
    getNetworkType() {
      uni.getNetworkType({
        success: (res) => {
          this.networkType = res.networkType;
          console.log('网络类型:', res.networkType);
        }
      });
    },
    
    refreshImages() {
      // 强制刷新图片
      this.$forceUpdate();
      uni.showToast({
        title: '已刷新',
        icon: 'success'
      });
    },
    
    testNetworkImages() {
      this.testImages = [
        'https://img.picui.cn/free/2025/06/20/685519e2eb24f.jpeg',
        'https://img.picui.cn/free/2025/06/20/68551abd65aa7.png',
        'https://via.placeholder.com/300x300.png'
      ];
    },
    
    testLocalImages() {
      this.testImages = [
        '/static/carType.svg',
        '/static/electric.svg',
        '/static/motorType.svg'
      ];
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 30rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.test-section, .debug-section, .action-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.debug-info {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.debug-info text {
  font-size: 28rpx;
  color: #666;
  padding: 10rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
}

.action-section {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.refresh-btn, .test-btn {
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
  border: none;
}

.refresh-btn {
  background-color: #2349A4;
  color: white;
}

.test-btn {
  background-color: #f0f0f0;
  color: #333;
}
</style>
