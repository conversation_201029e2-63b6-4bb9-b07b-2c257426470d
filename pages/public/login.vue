<template>
  <view class="container">
    <!-- 添加背景图 -->
    <view class="header-bg">
      <image class="bg" src="/static/login-home.png"></image>
    </view>

    <!-- 整合后的表单容器 -->
    <view class="integrated-form-container">
      <view class="tabs">
        <view :class="['tab', activeTab === 'register' ? 'active' : '']" @click="activeTab = 'register'">
          注册
        </view>
        <view :class="['tab', activeTab === 'login' ? 'active' : '']" @click="activeTab = 'login'">
          登录
        </view>
      </view>

      <!-- 登录表单 -->
      <uni-forms v-if="activeTab === 'login'" ref="loginForm" :model="loginForm" :rules="loginRule">
        <uni-forms-item name="username">
          <view class="input-item">
            <uni-icons type="person" size="20" color="#999"></uni-icons>
            <input v-model="loginForm.username" placeholder="请输入手机号"/>
          </view>
        </uni-forms-item>
        <uni-forms-item name="password">
          <view class="input-item">
            <uni-icons type="locked" size="20" color="#999"></uni-icons>
            <input type="password" v-model="loginForm.password" placeholder="请输入密码"/>
          </view>
        </uni-forms-item>

        <button class="confirm-btn" @click="submitLogin">登录</button>
        <!-- 添加微信授权登录按钮 -->
        <button class="wechat-login-btn" @click="wechatLogin">
          <image src="/static/wechat-icon.png" class="wechat-icon"></image>
          微信授权登录
        </button>
        <!-- 添加忘记密码链接 -->
        <view class="forgot-password">
          <text @click="goToResetPassword">忘记密码?</text>
        </view>
        <!-- 管理员登录 -->
        <view class="admin-login">
          <text class="admin-btn" @click="goToAdminLogin">管理员/员工登录</text>
        </view>
      </uni-forms>

      <!-- 注册表单 -->
      <uni-forms v-else ref="registerForm" :model="registerForm">
        <uni-forms-item name="nickname">
          <view class="input-item">
            <uni-icons type="person" size="20" color="#999"></uni-icons>
            <input v-model="registerForm.nickname" placeholder="请输入姓名"/>
          </view>
        </uni-forms-item>
        <uni-forms-item name="username">
          <view class="input-item">
            <uni-icons type="phone" size="20" color="#999"></uni-icons>
            <input v-model="registerForm.username" placeholder="请输入手机号"/>
          </view>
        </uni-forms-item>
        <uni-forms-item name="password">
          <view class="input-item">
            <uni-icons type="locked" size="20" color="#999"></uni-icons>
            <input type="password" v-model="registerForm.password" placeholder="请输入密码"/>
          </view>
        </uni-forms-item>
        <uni-forms-item name="rePassword">
          <view class="input-item">
            <uni-icons type="locked" size="20" color="#999"></uni-icons>
            <input type="password" v-model="registerForm.rePassword" placeholder="请再次输入密码"/>
          </view>
        </uni-forms-item>
        <view v-if="invitationCode" class="invitation-code">
          <text class="invitation-label">邀请码</text>
          <view class="invitation-value">
            <text>{{invitationCode}}</text>
          </view>
        </view>
        <button class="confirm-btn" @click="submitRegister">注册</button>
      </uni-forms>
    </view>

  </view>
</template>

<script>
import {
  mapMutations
} from 'vuex';
import {memberLogin, memberInfo, memberRegister, wxLogin, getWxOpenid, updateWxOpenid} from '@/api/member.js';

export default {
  // 移除不正确的组件导入
  data() {
    return {
      activeTab: 'login',
      invitationCode: '',
      sourceType: null,
      loginForm: {
        username: '',
        password: ''
      },
      registerForm: {
        nickname: '',
        username: '',
        password: '',
        rePassword: '',
        sourceType: null,
        invitationCode: ''
      },
      loginRule: {
        username: {
          rules: [{
            required: true,
            errorMessage: '请输入手机号'
          }]
        },
        password: {
          rules: [{
            required: true,
            errorMessage: '请输入密码'
          }]
        }
      },
      registerRule: {
        nickname: {
          rules: [{
            required: true,
            errorMessage: '请输入姓名'
          }]
        },
        username: {
          rules: [{
            required: true,
            errorMessage: '请输入手机号'
          }]
        },
        password: {
          rules: [{
            required: true,
            errorMessage: '请输入密码'
          }]
        },
        rePassword: {
          rules: [{
            required: true,
            errorMessage: '请再次输入密码'
          }]
        }
      }
    }
  },
  onLoad(option) {
    console.log(option)
    this.invitationCode = option.invitationCode;
    this.registerForm.invitationCode = option.invitationCode;
    this.activeTab = option.invitationCode ? 'register' : option.activeTab ? option.activeTab :'login';
    this.sourceType = option.sourceType ? option.sourceType : null;
    this.registerForm.sourceType = this.sourceType;
    this.registerForm.invitationCode = this.invitationCode;
    console.log(this.invitationCode);
  },
  // 修改方法部分
  methods: {
    async submitLogin() {
      try {
        await this.$refs.loginForm.validate()
        await this.handleLogin()
      } catch (e) {
        console.log('表单校验失败', e)
      }
    },

    async submitRegister() {
      try {
        await this.handleRegister()
      } catch (e) {
        console.log('表单校验失败', e)
      }
    },
    ...mapMutations(['login']),
    async handleLogin() {
      this.logining = true;
      try {
        const response = await memberLogin({
          username: this.loginForm.username,
          password: this.loginForm.password
        });

        // 保存token
        const token = response.data.tokenHead + response.data.token;
        uni.setStorageSync('token', token);

        // 获取并保存用户信息
        const userInfoResponse = await memberInfo();
        this.login(userInfoResponse.data);

        uni.reLaunch({
          url: '/pages/user/index'
        });
      } catch (error) {
        uni.showToast({
          title: error.data.message,
          icon: 'none'
        });
      } finally {
        this.logining = false;
      }
    },
    async handleRegister() {
      // 注册逻辑
      try {
        if (this.registerForm.nickname === '') {
          uni.showToast({
            title: '请输入姓名',
            icon: 'none'
          });
          return;
        }
        // 加强手机号格式校验
        if(!/^1[3-9]\d{9}$/.test(this.registerForm.username)) {
          uni.showToast({
            title: '请输入正确的11位手机号',
            icon: 'none'
          });
          return;
        }
        if (this.registerForm.password === '') {
          uni.showToast({
            title: '请输入密码',
            icon: 'none'
          });
          return;
        }
        if (this.registerForm.rePassword === '') {
          uni.showToast({
            title: '请再次输入密码',
            icon: 'none'
          });
          return;
        }
        if (this.registerForm.password.length < 6) {
          uni.showToast({
            title: '密码长度不能少于6位',
            icon: 'none'
          });
          return;
        }
        if (this.registerForm.password !== this.registerForm.rePassword) {
          uni.showToast({
            title: '两次输入的密码不一致',
            icon: 'none'
          });
          return;
        }
        const res = await memberRegister(this.registerForm);
        // 处理注册成功逻辑
        console.log(res)
        if (res.code === 200) {
          uni.showToast({
            title: '注册成功',
            icon: 'success'
          });
         this.activeTab = 'login'
        }
      } catch (error) {
        // 处理错误
        console.error('注册失败:', error);
      }
    },
    goToAdminLogin() {
      console.log('点击了管理员登录按钮');
      uni.showLoading({
        title: '跳转中...'
      });

      try {
        uni.navigateTo({
          url: '/pages/public/admin-login',
          success: function () {
            console.log('跳转成功');
            uni.hideLoading();
          },
          fail: function (err) {
            console.error('跳转失败:', err);
            uni.hideLoading();
            uni.showToast({
              title: '跳转失败，请重试',
              icon: 'none'
            });
          }
        });
      } catch (error) {
        console.error('跳转异常:', error);
        uni.hideLoading();
        uni.showToast({
          title: '发生异常，请重试',
          icon: 'none'
        });
      }
    },
    goToResetPassword() {
      uni.navigateTo({
        url: '/pages/public/reset-password'
      });
    },
    async wechatLogin() {
      try {
        const that = this;
        // 调用微信登录接口获取 code
        uni.login({
          provider: 'weixin', //使用微信登录
          success: async (loginRes) => {
            console.log(loginRes);
            if (loginRes) {
              // 调用后端接口完成微信登录
              await wxLogin({code: loginRes.code}).then(async response => {
                if (response.code === 405) {
                  uni.showToast({
                    title: '请先登录，然后绑定微信账号',
                    icon: 'none'
                  })
                } else {
                  // 保存 token
                  const token = response.data.tokenHead + response.data.token;
                  uni.setStorageSync('token', token);

                  // 获取并保存用户信息
                  const userInfoResponse = await memberInfo();
                  that.login(userInfoResponse.data);

                  uni.reLaunch({
                    url: '/pages/user/index'
                  });
                }

              });

            } else {
              uni.showToast({
                title: '获取微信登录凭证失败',
                icon: 'none'
              });
            }
          }
        });

      } catch (error) {
        console.error('微信登录出错:', error);
        uni.showToast({
          title: error.data?.message || '微信登录失败，请重试',
          icon: 'none'
        });
      }
    }
  }
}
</script>

<style lang='scss'>
.container {
  padding: 0 30upx;
  position: relative;
  min-height: 100vh;
  box-sizing: border-box;
}

// 修改.integrated-form-container样式
.integrated-form-container {
  margin-top: -180upx;
  background: #fff;
  border-radius: 20upx;
  padding: 30upx; // 增加容器内边距
  box-shadow: 0 4upx 20upx rgba(0, 0, 0, 0.15);
  position: relative;
  z-index: 2;
  margin-bottom: 100upx;
  overflow: hidden;
}

// 修改.input-item样式
.input-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 20upx;
  background: $page-color-light;
  height: 100upx;
  border-radius: 4px;
  margin-bottom: 30upx;

  .input-icon {
    margin-right: 20upx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  input {
    height: 60upx;
    font-size: $font-base + 2upx;
    color: $font-color-dark;
    width: 100%;
  }
}

// 修改.confirm-btn样式
.confirm-btn {
  width: calc(100% - 20upx); // 调整宽度
  margin: 30upx 10upx 0; // 调整外边距
  height: 90upx;
  line-height: 90upx;
  border-radius: 8px;
  background: #2349A4;
  color: #fff;
  font-size: $font-lg;
  margin-top: 40upx;
  border: none;
}

// 修改.form-content样式
.form-content {
  padding: 0 20upx 40upx; // 左右间距从30upx改为20upx
  margin-top: 0;
  border-top: none;
}

.header-bg {
  height: 600upx;
  margin: 0 -30upx 30upx -30upx;
  position: relative;
  margin-top: 0;

  .bg {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
  }
}

.tabs {
  display: flex;
  margin-top: -40upx;
  margin-bottom: 20upx; // 添加底部间距
  background: #fff;
  border-radius: 20upx 20upx 0 0;
  padding-top: 40upx;
}

.tab {
  flex: 1;
  text-align: center;
  padding: 30upx 0;
  font-size: 36upx;
  font-weight: 500;
  border-bottom: none; // 明确移除边框

  &.active {
    color: #007AFF;
    font-weight: bold;
    position: relative;

    &:after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 40%;
      height: 4upx;
      background: #007AFF;
    }
  }
}

.form-content {
  padding: 0 40upx 40upx;
  margin-top: 0; // 移除负边距
  border-top: none; // 确保没有上边框
}

// 修改.input-item样式
.input-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 20upx; // 左右间距从30upx改为20upx
  background: $page-color-light;
  height: 100upx;
  border-radius: 4px;
  margin-bottom: 30upx;

  .input-icon {
    margin-right: 20upx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  input {
    height: 60upx;
    font-size: $font-base + 2upx;
    color: $font-color-dark;
    width: 100%;
  }
}

// 调整按钮间距
.confirm-btn {
  width: calc(100% - 20upx);
  height: 80upx; // 减少高度
  line-height: 80upx;
  border-radius: 8upx;
  background: #2349A4;
  color: #fff;
  font-size: 30upx; // 减小字体大小
  margin-top: 30upx; // 减少顶部间距
  border: none;
  transition: all 0.3s;
}

.uni-forms-item {
  margin-bottom: -8px !important;
}

// 添加错误提示样式
.uni-forms-item__error {
  color: #ff4d4f;
  font-size: 24upx;
  margin-top: -20upx;
  margin-bottom: 20upx;
  padding-left: 30upx;
}

.safe-area {
  height: 100upx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 30upx;
  margin-bottom: 30upx;

  .admin-link {
    color: #2349A4;
    font-size: 32upx;
    text-align: center;
    padding: 20upx 40upx;
    border-radius: 30upx;
    background-color: rgba(35, 73, 164, 0.1);
    /* 增加点击区域 */
    min-width: 200upx;
    min-height: 80upx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.invitation-code {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  margin-bottom: 30rpx;

  .invitation-label {
    font-size: 28rpx;
    color: #333;
    margin-right: 20rpx;
  }

  .invitation-value {
    flex: 1;
    background: #F8F8F8;
    padding: 16rpx 24rpx;
    border-radius: 8rpx;

    text {
      font-size: 28rpx;
      color: #2349A4;
      font-weight: 500;
    }
  }
}

// 添加忘记密码样式
.forgot-password {
  text-align: right;
  padding: 10px 10upx;
  margin-bottom: 20upx;

  text {
    color: #2349A4;
    font-size: 28upx;
  }
}

// 修改管理员登录按钮样式
.admin-login {
  margin-top: 30upx;
  text-align: center;

  .admin-btn {
    display: inline-block;
    padding: 16upx 32upx;
    border-radius: 50upx;
    background: linear-gradient(135deg, #2349A4, #3A6ED8);
    color: #fff;
    font-size: 28upx;
    box-shadow: 0 4upx 12upx rgba(35, 73, 164, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }
}

.wechat-login-btn {
  width: calc(100% - 20upx);
  margin: 30upx 10upx 0;
  height: 90upx;
  line-height: 90upx;
  border-radius: 8upx;
  background: #2349A4;
  color: #fff;
  font-size: $font-lg;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.wechat-icon {
  width: 40upx;
  height: 40upx;
  margin-right: 20upx;
}
</style>

