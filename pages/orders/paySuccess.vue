<template>
	<view class="content">
		<uni-icons
			:type="payText === '支付成功' ? 'checkmarkempty' : 'closeempty'"
			:color="payText === '支付成功' ? '#18bc37' : '#e43d33'"
			size="60"
			class="status-icon"
		></uni-icons>
		<text class="tit">{{payText}}</text>
		<view class="success-message">
			<view class="message-icon">📅</view>
			<view class="message-content">
				<text class="message-text">您的车辆检测服务已预约成功</text>
				<text class="queue-info">当天排在 <text class="highlight-number">{{ appointmentSort }}</text> 位</text>
			</view>
			</view>
		<view class="btn-group">
      <view class="mix-btn" @click="queryOrderNo(payResponse.outTradeNo)">查看订单</view>
			<view class="mix-btn hollow" @click="backOrders()">返回</view>
		</view>
	</view>
</template>

<script>
	import uniIcons from '@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue'
  import {fetchAliapyStatus, fetchWxpayStatus, queryAppointmentSort, queryOrderNo} from '@/api/vehicleOrder.js';
	import { USE_ALIPAY } from '@/utils/appConfig.js';
	export default {
		components: {
			uniIcons
		},
		data() {
			return {
				payText: '',
				tradeStatus: null,
        appointmentSort: null,
        orderNo: null,
				// 支付宝回调参数
				payResponse: {
					charset: '',
					outTradeNo: '',
					method: '',
					totalAmount: '',
					sign: '',
					tradeNo: '',
					authAppId: '',
					version: '',
					appId: '',
					signType: '',
					sellerId: '',
					timestamp: ''
				}
			}
		},
		onLoad(options) {
			// 映射参数到payResponse中
			this.payResponse = {
				charset: options.charset,
				outTradeNo: options.out_trade_no,
				method: options.method,
				totalAmount: options.total_amount,
				sign: options.sign,
				tradeNo: options.trade_no,
				authAppId: options.auth_app_id,
				version: options.version,
				appId: options.app_id,
				signType: options.sign_type,
				sellerId: options.seller_id,
				timestamp: options.timestamp,
        payMethod: options.payMethod,
			};

			console.log('支付响应参数：', this.payResponse);

			if(!USE_ALIPAY){
				this.payText = '支付成功';
				// 即使不使用支付宝，也查询预约排序
				this.queryAppointmentSortInfo();
				return;
			}
      console.log('订单号：', this.payResponse.outTradeNo);
      // this.queryOrderNo(this.alipayParams.outTradeNo);
			// 查询支付状态
      if (this.payResponse.payMethod === 'wxpay') {
        fetchWxpayStatus({outTradeNo: this.payResponse.outTradeNo})
            .then(response => {
              console.log('支付状态查询成功:', response);
              this.tradeStatus = response.data;
              if(response.data != null && 'SUCCESS' === response.data.tradeState){
                this.payText = '支付成功';
                // 支付成功后查询预约排序
                this.queryAppointmentSortInfo();
              } else {
                this.payText = '支付失败';
              }
              console.log(this.tradeStatus);
            })
      } else {
        fetchAliapyStatus({outTradeNo: this.payResponse.outTradeNo})
            .then(response => {
              this.tradeStatus = response.data;
              if(this.tradeStatus != null && 'TRADE_SUCCESS' === this.tradeStatus){
                this.payText = '支付成功';
                // 支付成功后查询预约排序
                this.queryAppointmentSortInfo();
              } else {
                this.payText = '支付失败';
              }
              console.log(this.tradeStatus);
            })
            .catch(error => {
              console.error('查询支付状态失败:', error);
              this.payText = '支付状态查询失败';
            });
      }

		},
		methods: {
			// 将查询预约排序逻辑提取为方法
			queryAppointmentSortInfo() {
				queryAppointmentSort({tradeNo: this.payResponse.outTradeNo})
					.then(response => {
						this.appointmentSort = response.data;
					})
					.catch(error => {
						console.error('查询预约排序失败:', error);
						this.appointmentSort = '--';
					});
			},
      queryOrderNo(outTradeNo) {
        queryOrderNo({outTradeNo: outTradeNo})
          .then(response => {
            this.orderNo = response.data;
            uni.reLaunch({
              url: '/pages/orders/orderDetail?orderNo=' + this.orderNo + '&fromPaySuccess=true'
            })
          })
          .catch(error => {
            console.error('查询订单号失败:', error);
            this.orderNo = '--';
          });
      },
			backOrders() {
				uni.reLaunch({
					url: '/pages/orders/order?state=1'
				})
			}
		}
	}
</script>

<style lang='scss'>
	.content{
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
	}
	.status-icon {
		margin-top: 100rpx;
	}
	.tit{
		font-size: 38rpx;
		color: #303133;
		margin-bottom: 40rpx;
	}
	.success-message {
		display: flex;
		align-items: center;
		background: linear-gradient(to right, #f0f8ff, #ffffff);
		padding: 30rpx;
		border-radius: 20rpx;
		margin: 20rpx 30rpx;
		width: 80%;
		box-shadow: 0 8rpx 20rpx rgba(35, 73, 164, 0.1);
		border: 1px solid #e0e0e0;
	}
	.message-icon {
		font-size: 50rpx;
		margin-right: 20rpx;
	}
	.message-content {
		display: flex;
		flex-direction: column;
	}
	.message-text {
		font-size: 30rpx;
		color: #333;
		margin-bottom: 10rpx;
	}
	.queue-info {
		font-size: 28rpx;
		color: #666;
	}
	.highlight-number {
		color: #2349A4;
		font-size: 36rpx;
		font-weight: bold;
		background-color: #f0f4ff;
		padding: 0 16rpx;
		border-radius: 8rpx;
	}
	.btn-group{
		padding-top: 100upx;
	}
	.mix-btn {
		margin-top: 30upx;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 600upx;
		height: 80upx;
		font-size: $font-lg;
		color: #fff;
		background-color: #2349A4;
		border-radius: 10upx;
		&.hollow{
			background: #fff;
			color: #303133;
			border: 1px solid #ccc;
		}
	}
</style>
