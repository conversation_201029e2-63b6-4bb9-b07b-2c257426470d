<template>
  <view>
    <!-- 自定义导航栏 -->
    <view class="custom-nav">
      <view class="nav-left" @click="goBack">
        <uni-icons type="back" size="24" color="#fff"></uni-icons>
      </view>
      <view class="nav-title">订单详情</view>
    </view>

    <!-- 原有的状态部分 -->
    <view class="status-section">
      <image :src="orderStatus.image" class="icon"/>
      <view class="status-info">
        <text class="label-text">
          {{ order.orderStatus | formatOrderStatus }}
        </text>
        <text class="cancel-time" v-if="(order.orderStatus === '3') && order.cancelTime">
          {{ order.cancelTime | formatDateTime }}
        </text>
        <text class="cancel-time" v-if="(order.orderStatus === '4') && order.cancelTime">
          {{ order.cancelTime | formatDateTime }}
        </text>
      </view>
    </view>

    <scroll-view class="scrollable-content" scroll-y="true" :show-scrollbar="false" enhanced>
      <!-- 地址 -->
      <view class="address-section">
        <view class="order-content">
          <view class="icon-wrapper">
            <uni-icons type="location" size="24" color="#2349A4"></uni-icons>
          </view>
          <view class="cen">
            <view class="top">
              <text class="name">{{ order.clientName }}</text>
              <text class="mobile">{{ order.clientPhone }}</text>
            </view>
          </view>
        </view>

        <image class="a-bg"
               src="data:image/png;base64,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"></image>
      </view>

      <view class="goods-section">
        <view class="g-header b-b">
          <text class="name">订单信息</text>
        </view>
        <!-- 商品列表 -->
        <view class="g-item">
          <image :src="order.vehicleType === 'car' ? '/static/carType.svg' : order.vehicleType === 'electric' ? '/static/electric.svg' : '/static/motorType.svg'"></image>
          <view class="right">
            <text class="title clamp">{{ order.vehicleType === 'car' ? '汽车检测服务' : (order.vehicleType === 'electric'  ? '电动汽车检测服务' : '摩托车检测服务') }}</text>
            <text class="spec clamp">车牌号：{{ order.plateNumber }}</text>
            <text class="spec">预约时间：{{ order.appointmentTime }}</text>
            <text v-if="order.isHomePickup" class="spec clamp ">取车地址：{{ order.pickupAddress }}</text>
          </view>
        </view>
      </view>

      <!-- 金额明细 -->
      <view class="yt-list">
        <view class="yt-list-cell b-b">
          <text class="cell-tit clamp">总金额</text>
          <text class="cell-tip">￥{{ order.totalAmount }}</text>
        </view>
        <view class="yt-list-cell b-b">
          <text class="cell-tit clamp">检查费</text>
          <text class="cell-tip">￥{{ order.payAmount }}</text>
        </view>
        <view v-if="order.isHomePickup" class="yt-list-cell b-b">
          <text class="cell-tit clamp">取车费</text>
          <text class="cell-tip">￥{{ order.pickAmount }}</text>
        </view>
        <view class="yt-list-cell desc-cell">
          <text class="cell-tit clamp">备注</text>
          <text class="cell-tip">{{ order.remark }}</text>
        </view>
      </view>

      <!-- 订单明细 -->
      <view class="yt-list">
        <view class="yt-list-cell b-b">
          <text class="cell-tit clamp">订单编号</text>
          <text class="cell-tip">{{ order.orderNo }}</text>
        </view>
        <view class="yt-list-cell b-b">
          <text class="cell-tit clamp">提交时间</text>
          <text class="cell-tip">{{ order.submitTime | formatDateTime }}</text>
        </view>
        <view class="yt-list-cell b-b">
          <text class="cell-tit clamp">支付方式</text>
          <text class="cell-tip">{{ order.payType | formatPayType }}</text>
        </view>
        <view class="yt-list-cell b-b" v-if="order.payStatus !== 0">
          <text class="cell-tit clamp">实付金额</text>
          <text class="cell-tip">￥{{ order.actualAmount }}</text>
        </view>
        <view class="yt-list-cell b-b" v-if="order.couponAmount && order.couponAmount > 0">
          <text class="cell-tit clamp">优惠金额</text>
          <text class="cell-tip">￥{{ order.couponAmount }}</text>
        </view>
        <view class="yt-list-cell b-b" v-if="order.payStatus !== 0">
          <text class="cell-tit clamp">付款时间</text>
          <text class="cell-tip">{{ order.paymentTime | formatDateTime }}</text>
        </view>
        <view class="yt-list-cell b-b" v-if="order.payStatus===4">
          <text class="cell-tit clamp">退款时间</text>
          <text class="cell-tip">{{ order.cancelTime | formatDateTime }}</text>
        </view>
        <view class="yt-list-cell b-b" v-if="order.payStatus===4">
          <text class="cell-tit clamp">退款金额</text>
          <text class="cell-tip">￥{{ order.refundAmount }}</text>
        </view>
      </view>

      <!-- 取车图片 -->
      <view class="yt-list" v-if="order.pickedImgList && order.pickedImgList.length > 0">
        <view class="yt-list-cell b-b">
          <text class="cell-tit clamp">取车图片</text>
        </view>
        <view class="image-container">
          <upload-images
              :initialImages="order.pickedImgList"
              :readonly="true"
              :length="order.pickedImgList.length"
          />
        </view>
      </view>

      <!-- 还车图片 -->
      <view class="yt-list" v-if="order.returnImgList && order.returnImgList.length > 0">
        <view class="yt-list-cell b-b">
          <text class="cell-tit clamp">还车图片</text>
        </view>
        <view class="image-container">
          <upload-images
              :initialImages="order.returnImgList"
              :readonly="true"
              :length="order.returnImgList.length"
          />
        </view>
      </view>
      <view style="padding-bottom: 100rpx"></view>
    </scroll-view>

    <!-- 底部 -->
    <view class="footer" v-if="order.appointmentStatus === '1' && order.payStatus===0">
      <view class="action-box b-t" v-if="order.payStatus===0">
        <button class="action-btn" @click="cancelOrder(order.id)">取消订单</button>
        <button class="action-btn recom" @click="payOrder(order)">立即付款</button>
      </view>
      <view class="price-content" v-if="order.payStatus===0">
        <text>应付金额</text>
        <text class="price-tip">￥</text>
        <text class="price">{{ order.totalAmount }}</text>
      </view>
    </view>

  </view>
</template>

<script>
import {
  fetchOrderDetail,
  cancelUserOrder,
  confirmReceiveOrder
} from '@/api/vehicleOrder.js';
import {
  formatDate
} from '@/utils/date';
import uploadImages from '@/components/upload-images.vue';

export default {
  components: {
    uploadImages
  },
  data() {
    return {
      orderNo: null,
      fromPaySuccess: false,
      order: {},
      orderStatus: {}
    }
  },
  onLoad(option) {
    // 获取传递过来的orderNo参数
    this.orderNo = option.orderNo;
    this.fromPaySuccess = option.fromPaySuccess === 'true';
    this.loadData();
  },
  filters: {
    formatProductAttr(jsonAttr) {
      let attrArr = JSON.parse(jsonAttr);
      let attrStr = '';
      for (let attr of attrArr) {
        attrStr += attr.key;
        attrStr += ":";
        attrStr += attr.value;
        attrStr += ";";
      }
      return attrStr
    },
    formatDateTime(time) {
      if (time == null || time === '') {
        return 'N/A';
      }
      let date = new Date(time);
      return formatDate(date, 'yyyy-MM-dd hh:mm:ss')
    },
    formatPayType(payType) {
      if (payType === 0) {
        return "未支付";
      } else if (payType === 1) {
        return "支付宝支付";
      } else if (payType === 2) {
        return "微信支付";
      }
      return null;
    },
    formatPayStatus(payStatus) {
      if (!payStatus || payStatus === 0) {
        return "未支付";
      } else if (payStatus === 1) {
        return "已支付";
      } else if (payStatus === 4) {
        return "已退款";
      }
      return null;
    },
    formatOrderStatus(orderStatus) {
      if (orderStatus === '-1') {
        return "待支付";
      } if (orderStatus === '0') {
        return "未开始";
      } else if (orderStatus === '1') {
        return "进行中";
      } else if (orderStatus === '2') {
        return "已完成";
      } else if (orderStatus === '3') {
        return "已取消";
      } else if (orderStatus === '4') {
        return "已退款";
      }
      return null;
    }
  },
  methods: {
    //生成确认单信息
    async loadData() {
      fetchOrderDetail(this.orderNo).then(response => {
        this.order = response.data;

        // 确保图片列表是数组
        if (!this.order.pickedImgList) {
          this.order.pickedImgList = [];
        }
        if (!this.order.returnImgList) {
          this.order.returnImgList = [];
        }

        // 处理图片列表，确保格式正确
        this.order.pickedImgList = this.formatImageList(this.order.pickedImgList);
        this.order.returnImgList = this.formatImageList(this.order.returnImgList);

        this.setOrderStatus(this.order.status);
      });
    },

    // 格式化图片列表，确保与upload-images组件兼容
    formatImageList(imgList) {
      if (!Array.isArray(imgList)) return [];

      return imgList.map(img => {
        if (typeof img === 'string') {
          return { src: img, filePath: img };
        } else if (typeof img === 'object') {
          return {
            src: img.src || img.url || img.filePath,
            filePath: img.src || img.url || img.filePath
          };
        }
        return img;
      });
    },
    submit() {
    },
    stopPrevent() {
    },
    //取消订单
    cancelOrder(orderId) {
      let superThis = this;
      uni.showModal({
        title: '提示',
        content: '是否要取消该订单？',
        success: function (res) {
          if (res.confirm) {
            uni.showLoading({
              title: '请稍后'
            })
            cancelUserOrder({
              id: orderId
            }).then(response => {
              uni.hideLoading();
              superThis.loadData();
            });
          } else if (res.cancel) {
            console.log('用户点击取消');
          }
        }
      });
    },
    //支付订单
    payOrder(order) {
      const orderInfo = {
        vehicleType: order.vehicleType,
        appointmentTime: order.appointmentTime,
        plateNumber: order.plateNumber,
        clientName: order.clientName,
        clientPhone: order.clientPhone,
        isHomePickup: order.isHomePickup,
        pickupAddress: order.pickupAddress,
        remark: order.remark,
        orderId: order.id,
        orderNo: order.orderNo,
        tradeNo: order.tradeNo,
        totalAmount: order.totalAmount,
        payAmount: order.payAmount,
        pickAmount: order.pickAmount
      };
      // 跳转到支付页面
      uni.navigateTo({
        url: '/pages/appointment/payment?orderInfo=' + encodeURIComponent(JSON.stringify(orderInfo)),
        success: () => {
          console.log('跳转成功');  // 添加调试日志
        },
        fail: (err) => {
          console.error('跳转失败：', err);  // 添加调试日志
          uni.showToast({
            title: '跳转失败，请重试',
            icon: 'none'
          });
        }
      });
    },
    //确认收货
    receiveOrder(orderId) {
      let superThis = this;
      uni.showModal({
        title: '提示',
        content: '是否要确认收货？',
        success: function (res) {
          if (res.confirm) {
            uni.showLoading({
              title: '请稍后'
            })
            confirmReceiveOrder({
              orderId: orderId
            }).then(response => {
              uni.hideLoading();
              superThis.loadData();
            });
          } else if (res.cancel) {
            console.log('用户点击取消');
          }
        }
      });
    },
    //设置订单状态信息
    setOrderStatus(status) {
      switch (status) {
        case 0:
          this.orderStatus = {
            text: '等待付款',
            image: '/static/icon_wait.png'
          }
          break;
        case 1:
          this.orderStatus = {
            text: '等待发货',
            image: '/static/icon_deliver.png'
          }
          break;
        case 2:
          this.orderStatus = {
            text: '等待收货',
            image: '/static/icon_receive.png'
          }
          break;
        case 3:
          this.orderStatus = {
            text: '交易完成',
            image: '/static/icon_finish.png'
          }
          break;
        case 4:
          this.orderStatus = {
            text: '交易关闭',
            image: '/static/icon_close.png'
          }
          break;
      }
      ;
    },
    goBack() {
      if (this.fromPaySuccess) {
        uni.reLaunch({
          url: '/pages/orders/order?state=1'
        });
      } else {
        uni.navigateBack({
          delta: 1
        });
      }
    }
  }
}
</script>

<style lang="scss">
page {
  background: $page-color-base;
  padding-top: 340upx; // 为固定的导航栏和状态部分留出空间
  padding-bottom: 100upx;
}

.custom-nav {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  //#ifdef WEB
  height: 90px;
  //#endif
  //#ifdef MP-WEIXIN
  height: 200rpx;
  //#endif
  background-color: #2349A4;
  display: flex;
  align-items: center;
  z-index: 999;
  padding-top: var(--status-bar-height);

  .nav-left {
    padding: 0 30rpx;
    display: flex;
    align-items: center;
    height: 100%;
  }

  .nav-title {
    color: #fff;
    font-size: 32rpx;
    font-weight: 500;
    flex: 1;
    text-align: center;
    padding-right: 88rpx; // 为了保持标题居中，右侧预留与左侧按钮相同的宽度
  }
}

.status-section {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  margin-top: calc(95rpx + var(--status-bar-height));
  height: 200upx;
  background-color: #2349A4;
  display: flex;
  align-items: center;
  padding: 0 30upx;
  z-index: 1000; // 确保状态部分在内容上方
}

.status-section .icon {
  width: 48upx;
  height: 48upx;
  margin-right: 20upx;
}

.status-section .status-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.status-section .label-text {
  color: #fff;
  font-size: 38rpx;
  line-height: 1.4;
}

.status-section .label-text.canceled {
  color: #FF8989;  /* 浅红色，与深蓝背景协调 */
}

.status-section .cancel-time {
  color: rgba(255, 255, 255, 0.7);  /* 半透明白色 */
  font-size: 28rpx;
  margin-top: 10rpx;
  line-height: 1.2;
}

.address-section {
  padding: 30upx 0;
  background: #fff;
  position: relative;

  .order-content {
    display: flex;
    align-items: center;
  }

  .icon-wrapper {
    display: flex;
    align-items: center;  // 图标垂直居中
    margin-right: 20rpx;  // 图标和文字之间的间距
    width: 40rpx;         // 固定宽度，保持对齐
  }

  .cen {
    flex: 1;

    .top {
      display: flex;
      align-items: center;  // 名字和电话号码垂直居中
      margin-bottom: 10rpx;
    }

    .name {
      font-size: 34rpx;
      margin-right: 24rpx;
    }

    .mobile {
      font-size: 28rpx;
    }

    .address {
      font-size: 28rpx;
      color: #999;
    }
  }

  .a-bg {
    position: absolute;
    left: 0;
    bottom: 0;
    display: block;
    width: 100%;
    height: 5upx;
  }
}

.goods-section {
  margin-top: 16upx;
  background: #fff;
  padding-bottom: 1px;

  .g-header {
    display: flex;
    align-items: center;
    height: 84upx;
    padding: 0 30upx;
    position: relative;
  }

  .logo {
    display: block;
    width: 50upx;
    height: 50upx;
    border-radius: 100px;
  }

  .name {
    font-size: 30upx;
    color: $font-color-base;
    margin-left: 24upx;
  }

  .g-item {
    display: flex;
    margin: 20upx 30upx;

    image {
      flex-shrink: 0;
      display: block;
      width: 140upx;
      height: 140upx;
      border-radius: 4upx;
    }

    .right {
      flex: 1;
      padding-left: 24upx;
      overflow: hidden;
    }

    .title {
      font-size: 30upx;
      color: $font-color-dark;
    }

    .spec {
      font-size: 26upx;
      color: $font-color-light;
    }

    .promotion {
      font-size: 24upx;
      color: $base-color;
    }

    .price-box {
      display: flex;
      align-items: center;
      font-size: 32upx;
      color: $font-color-dark;
      padding-top: 10upx;

      .price {
        margin-bottom: 4upx;
      }

      .number {
        font-size: 26upx;
        color: $font-color-base;
        margin-left: 20upx;
      }
    }

    .step-box {
      position: relative;
    }
  }
}

.yt-list {
  margin-top: 16upx;
  background: #fff;
}

.yt-list-cell {
  display: flex;
  align-items: center;
  padding: 10upx 30upx 10upx 40upx;
  line-height: 55upx;
  position: relative;

  &.cell-hover {
    background: #fafafa;
  }

  &.b-b:after {
    left: 30upx;
  }

  .cell-icon {
    height: 32upx;
    width: 32upx;
    font-size: 22upx;
    color: #fff;
    text-align: center;
    line-height: 32upx;
    background: #f85e52;
    border-radius: 4upx;
    margin-right: 12upx;

    &.hb {
      background: #ffaa0e;
    }

    &.lpk {
      background: #3ab54a;
    }

  }

  .cell-more {
    align-self: center;
    font-size: 24upx;
    color: $font-color-light;
    margin-left: 8upx;
    margin-right: -10upx;
  }

  .cell-tit {
    flex: 1;
    font-size: 26upx;
    color: $font-color-light;
    margin-right: 10upx;
  }

  .cell-tip {
    font-size: 26upx;
    color: $font-color-dark;

    &.disabled {
      color: $font-color-light;
    }

    &.active {
      color: $base-color;
    }

    &.red {
      color: $base-color;
    }
  }

  &.desc-cell {
    .cell-tit {
      max-width: 90upx;
    }
  }

  .desc {
    flex: 1;
    font-size: $font-base;
    color: $font-color-dark;
  }

  .integration {
    flex: 1;
    font-size: $font-base;
    color: $font-color-dark;
    text-align: right;
  }
}

/* 支付列表 */
.pay-list {
  padding-left: 40upx;
  margin-top: 16upx;
  background: #fff;

  .pay-item {
    display: flex;
    align-items: center;
    padding-right: 20upx;
    line-height: 1;
    height: 110upx;
    position: relative;
  }

  .icon-weixinzhifu {
    width: 80upx;
    font-size: 40upx;
    color: #6BCC03;
  }

  .icon-alipay {
    width: 80upx;
    font-size: 40upx;
    color: #06B4FD;
  }

  .icon-xuanzhong2 {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60upx;
    height: 60upx;
    font-size: 40upx;
    color: $base-color;
  }

  .tit {
    font-size: 32upx;
    color: $font-color-dark;
    flex: 1;
  }
}

.footer {
  position: fixed;
  flex-direction: row-reverse;
  left: 0;
  bottom: 0;
  z-index: 995;
  display: flex;
  align-items: center;
  width: 100%;
  //#ifdef MP-WEIXIN
  height: 90px;
  //#endif
  justify-content: space-between;
  font-size: 30upx;
  background-color: #fff;
  color: $font-color-base;
  box-shadow: 0 -1px 5px rgba(0, 0, 0, .1);

  .price-content {
    padding-left: 30upx;
  }

  .price-tip {
    color: $base-color;
    margin-left: 8upx;
  }

  .price {
    font-size: 36upx;
    color: $base-color;
  }

  .submit {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 280upx;
    height: 100%;
    color: #fff;
    font-size: 32upx;
    background-color: $base-color;
  }
}

/* 优惠券面板 */
.mask {
  display: flex;
  align-items: flex-end;
  position: fixed;
  left: 0;
  top: var(--window-top);
  bottom: 0;
  width: 100%;
  background: rgba(0, 0, 0, 0);
  z-index: 9995;
  transition: .3s;

  .mask-content {
    width: 100%;
    min-height: 30vh;
    max-height: 70vh;
    background: #f3f3f3;
    transform: translateY(100%);
    transition: .3s;
    overflow-y: scroll;
  }

  &.none {
    display: none;
  }

  &.show {
    background: rgba(0, 0, 0, .4);

    .mask-content {
      transform: translateY(0);
    }
  }
}

/* 优惠券列表 */
.coupon-item {
  display: flex;
  flex-direction: column;
  margin: 20upx 24upx;
  background: #fff;

  .con {
    display: flex;
    align-items: center;
    position: relative;
    height: 120upx;
    padding: 0 30upx;

    &:after {
      position: absolute;
      left: 0;
      bottom: 0;
      content: '';
      width: 100%;
      height: 0;
      border-bottom: 1px dashed #f3f3f3;
      transform: scaleY(50%);
    }
  }

  .left {
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex: 1;
    overflow: hidden;
    height: 100upx;
  }

  .title {
    font-size: 32upx;
    color: $font-color-dark;
    margin-bottom: 10upx;
  }

  .time {
    font-size: 24upx;
    color: $font-color-light;
  }

  .right {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 26upx;
    color: $font-color-base;
    height: 100upx;
  }

  .price {
    font-size: 44upx;
    color: $base-color;

    &:before {
      content: '￥';
      font-size: 34upx;
    }
  }

  .tips {
    font-size: 24upx;
    color: $font-color-light;
    line-height: 60upx;
    padding-left: 30upx;
  }

  .circle {
    position: absolute;
    left: -6upx;
    bottom: -10upx;
    z-index: 10;
    width: 20upx;
    height: 20upx;
    background: #f3f3f3;
    border-radius: 100px;

    &.r {
      left: auto;
      right: -6upx;
    }
  }
}

.action-box {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 100upx;
  position: relative;
  padding-right: 30upx;

  /* 移除边框 */
  &:after {
    display: none;
  }
}

.action-btn {
  width: 160upx;
  height: 60upx;
  margin: 0;
  margin-left: 24upx;
  padding: 0;
  text-align: center;
  line-height: 60upx;
  font-size: $font-sm + 2upx;
  color: $font-color-dark;
  background: #fff;
  border-radius: 100px;

  &:after {
    border-radius: 100px;
  }

  &.recom {
    background: #fff9f9;
    color: $base-color;

    &:after {
      border-color: #f7bcc8;
    }
  }
}

/* 图片容器样式 */
.image-container {
  padding: 10upx 20upx 30upx;
}
</style>
