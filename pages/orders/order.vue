<template>
	<view class="content" @touchmove.stop.prevent>
		<!-- 固定不滑动的头部区域 -->
		<view class="fixed-header">
			<!-- 头部背景 -->
			<view class="head_bg">
				<image src="/static/appointment-bg.png"></image>
				<text class="head_title">我的订单</text>
			</view>

			<!-- 主标签栏 -->
			<view class="navbar">
				<view v-for="(item, index) in filteredNavList" :key="index" class="nav-item"
					:class="{ current: tabCurrentIndex === index }" @click="tabClick(index)">
					{{ item.text }}
				</view>
			</view>

			<!-- 子标签栏 -->
      <!-- 添加子标签栏，仅在"未开始"标签下显示 -->
			<view class="sub-navbar" v-if="currentUserType === 'emp' && filteredNavList[tabCurrentIndex] && filteredNavList[tabCurrentIndex].subTabs">
				<view v-for="(subItem, subIndex) in filteredNavList[tabCurrentIndex].subTabs" :key="subIndex"
					class="sub-nav-item"
					:class="{ current: filteredNavList[tabCurrentIndex].currentSubTab === subIndex }"
					@click="subTabClick(subIndex)">
					{{ subItem.text }}
				</view>
			</view>

			<!-- 日期筛选 -->
      <!-- 添加美化的日期筛选，仅在"未开始"标签下显示 -->
			<view class="date-filter" v-if="currentUserType === 'emp' && filteredNavList[tabCurrentIndex] && filteredNavList[tabCurrentIndex].state === '0'">
				<view class="date-filter-label">
					<text>预约日期</text>
				</view>
				<picker
					mode="date"
					:value="selectedDate"
					@change="handleDateChange"
					class="date-picker"
				>
					<view class="date-filter-btn">
						<text>{{ selectedDate || '今天' }}</text>
						<text class="iconfont icon-down"></text>
					</view>
				</picker>
			</view>
		</view>

		<!-- 可滑动的列表区域 -->
		<swiper :current="tabCurrentIndex"
			class="swiper-box"
			:class="{
				'with-sub-tabs': filteredNavList[tabCurrentIndex] && filteredNavList[tabCurrentIndex].subTabs,
				'with-date-filter': filteredNavList[tabCurrentIndex] && filteredNavList[tabCurrentIndex].state === '0'
			}"
			duration="300"
			@change="changeTab"
			@touchmove.stop>
			<swiper-item class="tab-content" v-for="(tabItem, tabIndex) in filteredNavList" :key="tabIndex">
				<scroll-view
					class="list-scroll-content"
					scroll-y="true"
					@scrolltolower="loadData('add')"
					refresher-enabled="true"
					:refresher-triggered="isRefreshing"
					@refresherrefresh="onRefresh"
					@refresherrestore="onRestore"
					@refresherpulling="onPulling"
					:show-scrollbar="false"
					enhanced="true"
					bounces="false"
					@touchstart="handleTouchStart"
					@touchmove="handleTouchMove">
					<!-- 空白页 -->
					<empty v-if="orderList == null || orderList.length === 0"></empty>

					<!-- 订单列表 -->
					<view v-for="(item, index) in orderList" :key="index" class="order-item">
						<view class="i-top b-b">
							<text class="time">订单号：{{ item.orderNo }}</text>
              <image style="height: 100%; width: 70upx; margin-right: 25upx" :src="item.vehicleType === 'car' ? '/static/carType.svg' : item.vehicleType === 'electric' ? '/static/electric.svg' : '/static/motorType.svg'" mode="aspectFit"></image>
							<text class="state">{{ item.plateNumber }}</text>
						</view>

						<view @click="showOrderDetail(item.orderNo)">
							<view class="goods-box-single">
								<view class="right">
									<view>
										<text>姓名</text>
										<text class="title clamp">{{ item.clientName }}</text>
									</view>
									<view>
										<text>电话</text>
										<text class="attr-box">{{ item.clientPhone }}</text>
									</view>
									<view>
										<text>预约时间</text>
                    <text class="attr-box">
                      {{ item.appointmentTime.split(' ')[0] }}<br/>
                      {{ item.appointmentTime.split(' ').slice(1).join(' ') }}
                    </text>
									</view>
									<view v-if="item.isHomePickup">
										<text>取车地点</text>
										<text class="price">{{ item.pickupAddress }}</text>
									</view>
                  <view v-if="item.orderStatus === '2'">
                    <text>完成时间</text>
                    <text class="price">{{ item.cancelTime | formatDateTime }}</text>
                  </view>
                  <view v-if="item.orderStatus === '3'">
                    <text>取消时间</text>
                    <text class="price">{{ item.cancelTime | formatDateTime }}</text>
                  </view>
                  <view v-if="item.orderStatus === '4'">
                    <text>退款时间</text>
                    <text class="price">{{ item.cancelTime | formatDateTime }}</text>
                  </view>
									<view>
										<text>备注</text>
										<text class="attr-box">{{ item.remark }}</text>
									</view>
								</view>
							</view>
						</view>
						<upload-images
							v-if="item.orderStateNode === 'PICKING_UP' || item.orderStateNode === 'PICKED' || item.orderStateNode === 'START_INSPECTION' || item.orderStateNode === 'FINISHED_INSPECTION'"
							class="order-upload-images"
							:url="uploadUrl"
							:length="3"
							:count="3"
							:header="uploadHeader"
							:initialImages="item.imgList || []"
							:readonly="currentUserType === 'member' || currentUserType === 'admin'
							|| (currentUserType === 'emp' && (item.orderStateNode === 'PICKED' || item.orderStateNode === 'START_INSPECTION' || item.orderStateNode === 'COMPLETED') || item.orderStatus === '2')"
							:fileExtname="['jpg', 'jpeg', 'png']"
							@success="(res) => handleUploadSuccess(res, item)"
							@delete="(data) => handleDeleteImage(data, item)"
							ref="uploadImages"
						/>
						<view class="action-box b-t" v-if="item.orderStatus === '-1' && currentUserType === 'member'">
							<button class="action-btn cancel" @click="cancelOrder(item.id)">取消</button>
							<button class="action-btn recom" @click="payOrder(item)">去支付</button>
						</view>
						<view class="action-box b-t" v-if="item.orderStatus === '0' && currentUserType === 'member'">
							<button class="action-btn recom" @click="changeAppointmentDate(item.id)">更改时间</button>
							<button class="action-btn cancel" @click="refund(item)">取消</button>
						</view>
						<view class="action-box b-t" v-if="item.orderStatus === '1' && currentUserType === 'member'">
							<button class="action-btn recom" v-if="item.orderStateNode === 'PICKING_UP'">取车中</button>
							<button class="action-btn recom" v-if="item.orderStateNode === 'PICKED'" >已取车</button>
							<button class="action-btn recom" v-if="item.orderStateNode === 'START_INSPECTION'">检测中</button>
							<button class="action-btn recom" v-if="item.orderStateNode === 'FINISHED_INSPECTION'">检测完成</button>
						</view>
						<!-- 非上门取车且未开始状态的员工操作按钮 -->
						<view class="action-box b-t" v-if="!item.isHomePickup && item.orderStatus === '0' && currentUserType === 'emp' && item.orderStateNode === 'WAIT_INSPECTION'">
							<button class="action-btn recom" @click="startInspection(item.id)">开始检测</button>
						</view>

						<!-- 上门取车且未开始状态的员工操作按钮 -->
						<view class="action-box b-t" v-if="item.isHomePickup && item.orderStatus === '0' && currentUserType === 'emp' && item.orderStateNode === 'WAIT_PICK'">
							<button class="action-btn recom" @click="pickingUp(item.id)">开始取车</button>
						</view>

						<view class="action-box b-t" v-if="!item.isHomePickup && item.orderStatus === '1' && currentUserType === 'emp'">
							<button class="action-btn recom" v-if="item.orderStateNode === 'PICKED'" @click="startInspection(item.id)">开始检测</button>
							<button class="action-btn recom" v-if="item.orderStateNode === 'START_INSPECTION'" @click="finishInspection(item.id)">检测完成</button>
						</view>

						<view class="action-box b-t" v-if="item.isHomePickup && item.orderStatus === '1' && currentUserType === 'emp'">
							<button class="action-btn recom" v-if="item.orderStateNode === 'PICKING_UP'" @click="pickUp(item.id)">已取车</button>
							<button class="action-btn recom" v-if="item.orderStateNode === 'PICKED'" @click="startInspection(item.id)">开始检测</button>
							<button class="action-btn recom" v-if="item.orderStateNode === 'START_INSPECTION'" @click="finishInspection(item.id)">检测完成</button>
							<button class="action-btn recom" v-if="item.orderStateNode === 'FINISHED_INSPECTION'" @click="returnCar(item.id)">已还车</button>
						</view>
						<view class="action-box b-t" v-if="item.orderStatus === '3'">
							<button class="action-btn recom" @click="showOrderDetail(item.orderNo)">已取消</button>
						</view>
					</view>

					<uni-load-more :status="loadingType"></uni-load-more>

					<!-- 添加底部空白间距 -->
					<view class="bottom-gap"></view>
				</scroll-view>
			</swiper-item>
		</swiper>
    <!-- 添加时间选择弹窗 -->
    <view v-if="showTimePicker" class="picker-modal">
      <view class="mask" @click="closeTimePicker"></view>
      <view class="picker-container">
        <view class="picker-header">
          <text class="cancel" @click="closeTimePicker">取消</text>
          <text class="title">选择预约时间</text>
          <text class="confirm" @click="confirmTime">确定</text>
        </view>
        <picker-view
          :value="pickerValue"
          @change="handleTimeChange"
          class="picker-view"
        >
          <picker-view-column>
            <view class="item" v-for="(date, index) in dateList" :key="index">
              {{date}}
            </view>
          </picker-view-column>
          <picker-view-column>
            <view class="item" v-for="(time, index) in timeList[currentDateIndex]" :key="index"
                  :class="{ 'disabled-time': time.disabled }">
              {{time.text}}
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </view>
	</view>
</template>

<script>
import {mapState} from 'vuex';
import uploadImages from '@/components/upload-images.vue'
import uniLoadMore from '@/components/uni-load-more/uni-load-more.vue';
import empty from "@/components/empty";
import {formatDate} from '@/utils/date';
import {
  doCancelAppointment,
  doChangeAppointmentTime,
  doFinishInspection,
  doReturnVehicle,
  doStartInspection,
  fetchOrderList,
  pickUp,
  refund, refundWx,
  startPickUp
} from "@/api/vehicleOrder";
import {getDateRange} from '@/api/sysData'
import {queryAliapyStatus} from "@/api/payment";
import {CURRENT_ENVIRONMENT, UPLOAD_URL} from '@/utils/appConfig.js';

export default {
	components: {
    uploadImages,
		uniLoadMore,
		empty
	},
	data() {
		return {
			tabCurrentIndex: 0,
      uploadHeader: {
        'Authorization': uni.getStorageSync('token')
      },
      uploadUrl: UPLOAD_URL,  // 使用环境配置中的上传地址
			orderParam: {
				status: '1',
				pageNum: 1,
				pageSize: 5,
				isHomePickup: true, // 添加上门取车筛选参数
        appointmentDate: this.formatTodayDate()
			},
			orderList: [],
      isInitialized: false, // 添加初始化标志
      lastLoadTime: 0, // 记录上次加载时间
      loading: false, // 添加加载状态
			loadingType: 'more',
			navList: [{
        state: '-1',
        text: '待支付'
      },{
				state: '0',
				text: '未开始',
				subTabs: [
					{ text: '上门取车', isHomePickup: true },
					{ text: '非上门取车', isHomePickup: false }
				],
				currentSubTab: 0 // 当前选中的子标签索引
			},
			{
				state: '1',
				text: '进行中'
			},
			{
				state: '2',
				text: '已完成'
			},
			{
				state: '3',
				text: '已取消'
			},
      {
        state: '4',
        text: '已退款'
      },
			],
			dateList: [], // 日期列表
			timeList: [], // 时间列表
			currentDateIndex: 0, // 当前选中的日期索引
			showTimePicker: false, // 控制时间选择器显示
			selectedOrderId: null, // 当前选中的订单ID
			tempAppointmentTime: '', // 临时存储选择的时间
			pickerValue: [0, 0], // 添加这个用于记录选中值
			pickerRange: [[], []], // 添加 pickerRange 到 data
			countdownTimer: null, // 添加倒计时定时器
      selectedDate: this.formatTodayDate(),
      isRefreshing: false, // 下拉刷新状态
      startY: 0, // 添加触摸事件处理
		};
	},
	onLoad(options) {
		/**
		 * 修复app端点击除全部订单外的按钮进入时不加载数据的问题
		 * 替换onLoad下代码即可
		 */
		this.tabCurrentIndex = +options.state || 0;

		// #ifdef MP-WEIXIN || MP-ALIPAY || MP-BAIDU
		// 微信/支付宝/百度小程序特殊处理
		if (options.state === 0 || options.state === undefined) {
			this.loadData().catch(err => console.log('初始加载异常:', err));
		}
		// #endif

		// #ifndef MP-WEIXIN || MP-ALIPAY || MP-BAIDU
		// 其他平台直接加载
		this.loadData().catch(err => console.log('初始加载异常:', err));
		// #endif

		// 设置默认日期为今天
		this.selectedDate = this.formatTodayDate();

		// 如果当前标签是"未开始"，设置日期筛选参数
		if (this.tabCurrentIndex !== undefined &&
			this.filteredNavList[this.tabCurrentIndex] &&
			this.filteredNavList[this.tabCurrentIndex].state === '0') {
			this.orderParam.appointmentDate = this.selectedDate;
		}
	},
	filters: {
		formatDateTime(time) {
			if (time == null || time === '') {
				return 'N/A';
			}
			let date = new Date(time);
			return formatDate(date, 'yyyy-MM-dd hh:mm:ss')
		},
	},
  computed: {
    CURRENT_ENVIRONMENT() {
      return CURRENT_ENVIRONMENT
    },
    ...mapState(['hasLogin','userInfo']),
    currentUserType() {
        return this.userInfo?.userType || 'guest';
    },
    empName() {
      return this.userInfo?.nickname || 'guest';
    },
    pickerRange() {
      return [
        this.dateList,
        this.timeList[this.currentDateIndex] || []
      ]
    },
    // 添加计算属性，根据用户类型过滤导航列表
    filteredNavList() {
      if (this.currentUserType === 'emp') {
        // 员工用户不显示"待支付"选项
        return this.navList.filter(item => item.state !== '-1');
      }
      // 其他用户显示完整列表
      return this.navList;
    }
  },
	methods: {
    // 下拉刷新触发
    onRefresh() {
      // 更新刷新状态
      this.isRefreshing = true;

      // 刷新数据
      this.loadData('refresh').then(() => {
        // 完成刷新
        setTimeout(() => {
          this.isRefreshing = false;
        }, 500); // 稍微延迟一下，让用户能看到刷新效果
      }).catch(() => {
        this.isRefreshing = false;
      });
    },

    // 下拉刷新复位
    onRestore() {
      console.log('下拉刷新复位');
    },

    // 下拉刷新被下拉
    onPulling() {
      console.log('下拉刷新被下拉');
    },

    //获取订单列表
		loadData(type = 'refresh', subTabIndex) {
      // 添加防抖处理，避免频繁调用
      const now = Date.now();
      if (!subTabIndex && (this.loading || (now - this.lastLoadTime < 300))) {
        console.log(subTabIndex)
        console.log('loadData 防抖生效');
        return Promise.reject('防抖生效');
      }
      this.loading = true;
      this.lastLoadTime = now;
			if (type === 'refresh') {
				this.orderParam.pageNum = 1;
				this.orderList = []; // 清空列表
			} else {
				this.orderParam.pageNum++;
			}

			// 获取当前标签状态
			const currentTab = this.filteredNavList[this.tabCurrentIndex];
			this.orderParam.status = currentTab.state;

			if (this.loadingType === 'loading') {
				// 防止重复加载
				return Promise.reject('加载中');
			}

			this.loadingType = 'loading';
      if (this.currentUserType === 'emp') {
        if (this.orderParam.isHomePickup === null) {
          this.orderParam.isHomePickup = true;
        }
      } else {
        this.orderParam.isHomePickup = 1;
        this.orderParam.appointmentDate = null;
      }
			return fetchOrderList(this.orderParam).then(response => {
				let list = response.data.list || [];

				if (type === 'refresh') {
					this.orderList = list;
					this.loadingType = list.length >= this.orderParam.pageSize ? 'more' : 'noMore';
				} else {
					if (list.length > 0) {
						this.orderList = this.orderList.concat(list);
						this.loadingType = list.length >= this.orderParam.pageSize ? 'more' : 'noMore';
					} else {
						this.orderParam.pageNum--;
						this.loadingType = 'noMore';
					}
				}
        this.loading = false;
        return Promise.resolve();
			}).catch(error => {
				console.error('获取订单列表失败:', error);
				this.loadingType = 'more';
        this.loading = false;
				uni.showToast({
					title: '获取订单列表失败',
					icon: 'none'
				});
        return Promise.reject(error);
			});
		},
		//swiper 切换
		changeTab(e) {
			const index = e.target.current;
			if (this.tabCurrentIndex === index) {
				return; // 如果切换到当前标签，不做任何操作
			}

			this.tabCurrentIndex = index;
			this.orderList = []; // 先清空列表

			// 重置筛选参数
			this.orderParam.isHomePickup = true;

			// 获取当前标签
			const currentTab = this.filteredNavList[index];

			// 如果是"未开始"标签，设置日期筛选为今天
			if (currentTab.state === '0') {
				this.selectedDate = this.formatTodayDate();
				this.orderParam.appointmentDate = this.selectedDate;
			} else {
				// 其他标签不使用日期筛选
				this.orderParam.appointmentDate = null;
			}

			// 如果新标签有子标签，设置筛选参数
			if (currentTab.subTabs && currentTab.subTabs.length > 0) {
				const subTab = currentTab.subTabs[currentTab.currentSubTab || 0];
				this.orderParam.isHomePickup = subTab.isHomePickup;
			}

			this.loadData('refresh');
		},
		//顶部tab点击
		tabClick(index) {
			if (this.tabCurrentIndex === index) {
				return; // 如果点击的是当前标签，不做任何操作
			}

			this.tabCurrentIndex = index;
			this.orderList = []; // 先清空列表

			// 重置筛选参数
			this.orderParam.isHomePickup = null;

			// 获取当前标签
			const currentTab = this.filteredNavList[index];

			// 如果是"未开始"标签，设置日期筛选为今天
			if (currentTab.state === '0') {
				this.selectedDate = this.formatTodayDate();
				this.orderParam.appointmentDate = this.selectedDate;
			} else {
				// 其他标签不使用日期筛选
				this.orderParam.appointmentDate = null;
			}

			// 如果新标签有子标签，设置筛选参数
			if (currentTab.subTabs && currentTab.subTabs.length > 0) {
				const subTab = currentTab.subTabs[currentTab.currentSubTab || 0];
				this.orderParam.isHomePickup = subTab.isHomePickup;
			}

			this.loadData('refresh');
		},
    // 子标签点击
    subTabClick(subIndex) {
      // 获取当前主标签
      const currentTab = this.filteredNavList[this.tabCurrentIndex];

      // 如果点击的是已选中的子标签，不做任何操作
      if (currentTab.currentSubTab === subIndex) {
        return;
      }

      // 更新当前子标签索引
      currentTab.currentSubTab = subIndex;

      // 更新筛选参数
      const subTab = currentTab.subTabs[subIndex];
      this.orderParam.isHomePickup = subTab.isHomePickup;

      // 重新加载数据
      this.orderList = []; // 清空当前列表
      this.loadData('refresh', true);
    },
    pickingUp(orderId) {
      let superThis = this;
      uni.showModal({
        title: '提示',
        content: '是否要开始取车？',
        success: function (res) {
          if (res.confirm) {
            uni.showLoading({
              title: '请稍后'
            })
            startPickUp({ id: orderId }).then(response => {
              uni.hideLoading();
              superThis.loadData();
            });
          } else if (res.cancel) {
            console.log('用户点击取消');
          }
        }
      });
    },
    pickUp(orderId) {
      const item = this.orderList.find(order => order.id === orderId);
      if (!item) return;
      if (item.imgList.length === 0) {
        uni.showToast({
          title: '请先上传图片',
          icon: 'none'
        });
        return;
      }

      uni.showModal({
        title: '提示',
        content: '是否已取车？',
        success: (res) => {  // 使用箭头函数
          if (res.confirm) {
            uni.showLoading({
              title: '请稍后'
            })
            pickUp({
              id: orderId,
              imgList: item.imgList || [] // 使用对应订单项的 imgList
            }).then(response => {
              uni.hideLoading();
              this.loadData();
            }).catch(error => {
              uni.hideLoading();
              uni.showToast({
                title: '操作失败',
                icon: 'none'
              });
            });
          }
        }
      });
    },
    startInspection(orderId) {
      const item = this.orderList.find(order => order.id === orderId);
      if (!item) return;
      console.log('item:', item)

      uni.showModal({
        title: '提示',
        content: '是否开始检车？',
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: '请稍后'
            })
            doStartInspection({
              id: orderId,
              imgList: item.imgList || [] // 使用对应订单项的 imgList
            }).then(response => {
              uni.hideLoading();
              this.loadData();
            }).catch(error => {
              uni.hideLoading();
              uni.showToast({
                title: '操作失败',
                icon: 'none'
              });
            });
          }
        }
      });
    },
    finishInspection(orderId) {
      const item = this.orderList.find(order => order.id === orderId);
      if (!item) return;

      uni.showModal({
        title: '提示',
        content: '是否检测完成？',
        success: (res) => {  // 使用箭头函数避免 this 指向问题
          if (res.confirm) {
            uni.showLoading({
              title: '请稍后'
            })
            doFinishInspection({
              id: orderId,
              imgList: item.imgList || [] // 使用对应订单项的 imgList
            }).then(response => {
              uni.hideLoading();
              this.loadData();
            }).catch(error => {
              uni.hideLoading();
              uni.showToast({
                title: '操作失败',
                icon: 'none'
              });
            });
          }
        }
      });
    },
    returnCar(orderId) {
      const item = this.orderList.find(order => order.id === orderId);
      if (!item) return;
      if (item.imgList.length === 0) {
        uni.showToast({
          title: '请先上传图片',
          icon: 'none'
        });
        return;
      }
      uni.showModal({
        title: '提示',
        content: '是否还车？',
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: '请稍后'
            })
            doReturnVehicle({
              id: orderId,
              imgList: item.imgList || []
            }).then(response => {
              uni.hideLoading();
              this.loadData();
            }).catch(error => {
              uni.hideLoading();
              uni.showToast({
                title: '操作失败',
                icon: 'none'
              });
            });
          }
        }
      });
    },
		//取消订单
		cancelOrder(orderId) {
			let superThis = this;
			uni.showModal({
				title: '提示',
				content: '是否要取消该订单？',
				success: function (res) {
					if (res.confirm) {
						uni.showLoading({
							title: '请稍后'
						})
            doCancelAppointment({ id: orderId }).then(response => {
							uni.hideLoading();
							superThis.loadData();
						});
					} else if (res.cancel) {
						console.log('用户点击取消');
					}
				}
			});
		},
		//支付订单
		payOrder(order) {
     console.log(order)
      // 跳转到支付页面
      uni.navigateTo({
        url: '/pages/appointment/payment?orderInfo=' + encodeURIComponent(JSON.stringify(order)),
        success: () => {
          console.log('跳转成功');  // 添加调试日志
        },
        fail: (err) => {
          console.error('跳转失败：', err);  // 添加调试日志
          uni.showToast({
            title: '跳转失败，请重试',
            icon: 'none'
          });
        }
      });
		},
    refund(order) {
      // 判断是否是上门取车且预约日期是当天
      const isHomePickup = order.isHomePickup;
      const isSameDay = this.isAppointmentToday(order.appointmentTime);

      // 根据条件显示不同的提示信息
      let confirmContent = '';
      let refundAmount = order.totalAmount || 168; // 默认退款金额
      let isCoupon = false;
      if (order.couponAmount && order.couponAmount > 0) {
        console.log('订单有优惠券');
        isCoupon = true;
        refundAmount = Number((refundAmount - order.couponAmount).toFixed(2));
      }

      if (isHomePickup && isSameDay && this.userInfo.memberType !== 'PUBLIC_SECURITY') {
        if (isCoupon) {
          confirmContent = `您确定要取消此订单吗？取消后订单将无法恢复，退款${refundAmount}元将在1-3个工作日原路返回。`;
        } else {
          // 上门取车且预约日期是当天
          const pickupFee = order.pickAmount; // 上门费
          const actualRefund = Number((refundAmount - pickupFee).toFixed(2));
          confirmContent = `您选择的订单已超过免费取消时限，取消将扣除${pickupFee}元上门费，退款${actualRefund}元将在1-3个工作日原路返回`;
        }
      } else {
        // 其他情况
        confirmContent = `您确定要取消此订单吗？取消后订单将无法恢复，退款${refundAmount}元将在1-3个工作日原路返回。`;
      }
      uni.showModal({
        title: '提示',
        content: confirmContent,
        success: (res) => {
          if (res.confirm) {
            uni.showLoading({
              title: '请稍后'
            });

            if (order.payType === 1) {
              console.log('支付宝支付，发起退款');
              // 调用退款API
              refund({
                outTradeNo: order.tradeNo,
                subject: order.vehicleType === 'car' ? '汽车检测服务' : order.vehicleType === 'electric' ? '电动汽车检测服务' : '摩托车检测服务',
                refundReason: '取消订单',
                // 如果是上门取车且当天，添加扣除上门费的标记
                deductPickupFee: isHomePickup && isSameDay
              }).then(response => {
                console.log(response);
                uni.hideLoading();
                uni.showToast({
                  title: '请求成功，稍后更新结果。',
                  icon: 'success',
                  duration: 5000
                });

                // 延迟一下再刷新数据，确保后端有时间处理
                setTimeout(() => {
                  console.log('刷新订单列表');
                  this.loadData('refresh');  // 使用refresh模式强制刷新
                }, 500);
              }).catch(error => {
                uni.hideLoading();
                uni.showToast({
                  title: error.message || '取消失败，请重试',
                  icon: 'none'
                });
              });
            } else if (order.payType === 2) {
              console.log('微信支付，发起退款');
              // 调用微信退款API
              refundWx({
                outTradeNo: order.tradeNo,
                deductPickupFee: isHomePickup && isSameDay,
                subject: order.vehicleType === 'car' ? '汽车检测服务' : order.vehicleType === 'electric' ? '电动汽车检测服务' : '摩托车检测服务',
              }).then(response => {
                console.log(response);
                uni.hideLoading();
                uni.showToast({
                  title: '请求成功，稍后更新结果。',
                  icon: 'success',
                  duration: 5000
                });

                // 延迟一下再刷新数据，确保后端有时间处理
                setTimeout(() => {
                  console.log('刷新订单列表');
                  this.loadData('refresh');  // 使用refresh模式强制刷新
                }, 500);
              }).catch(error => {
                uni.hideLoading();
                uni.showToast({
                  title: error.message || '取消失败，请重试',
                  icon: 'none'
                });
              });
            }
          }
        }
      });
    },
    queryOrderPayStatus(item) {
      queryAliapyStatus({ orderNo: item.orderNo }).then(response => {
        console.log(response);
        if (response.data === 'TRADE_SUCCESS') {
          this.loadData();
        }
      });
    },
		//查看订单详情
		showOrderDetail(orderNo) {
			// 保存当前 tab 索引到本地存储
			uni.setStorageSync('orderListTabIndex', this.tabCurrentIndex);

			// 如果当前标签有子标签，也保存子标签索引
			const currentTab = this.filteredNavList[this.tabCurrentIndex];
			if (currentTab && currentTab.subTabs) {
				uni.setStorageSync('orderListSubTabIndex', currentTab.currentSubTab);
			}

			uni.navigateTo({
				url: `/pages/orders/orderDetail?orderNo=${orderNo}`
			});
		},
		handleDeleteImage(data, item) {
			// 确保 imgList 存在
			if (!item.imgList) {
				this.$set(item, 'imgList', []);
			}

			// 从数组中移除被删除的图片
			item.imgList.splice(data.index, 1);

			// 找到当前订单在 orderList 中的索引并更新
			const orderIndex = this.orderList.findIndex(order => order.id === item.id);
			if (orderIndex !== -1) {
				// 确保更新是响应式的
				this.$set(this.orderList, orderIndex, {...item});
			}
		},
		handleUploadSuccess(uploadResult, item) {
      console.log(item.imgList);
			if (uploadResult && uploadResult.data) {
				// 确保 imgList 是响应式的
				if (!item.imgList) {
					this.$set(item, 'imgList', []);
				}

				// 创建新的图片对象
				const newImage = {
					id: Date.now(),
					url: uploadResult.data.url,
					filePath: uploadResult.data.url,
          src: uploadResult.data.url
				};

				// 使用响应式方法更新数组
				item.imgList.push(newImage);

				// 找到当前订单在 orderList 中的索引
				const index = this.orderList.findIndex(order => order.id === item.id);
				if (index !== -1) {
					// 确保更新是响应式的
					this.$set(this.orderList, index, {...item});
				}

				console.log('After upload - item:', item);
				console.log('After upload - imgList:', item.imgList);
			}
		},
		// 打开时间选择器
		async changeAppointmentDate(orderId) {
			// 先获取数据
			await this.fetchTimeOptions();

			// 获取当前订单信息
			const currentOrder = this.orderList.find(order => order.id === orderId);
			if (!currentOrder) return;

			this.selectedOrderId = orderId;
			this.pickerValue = [0, 0];
			this.currentDateIndex = 0;

			// 禁用当前预约时间
			if (currentOrder.appointmentTime) {
				// 解析当前预约时间，例如："2023年5月20日 14:00-15:00"
				const currentAppointmentTime = currentOrder.appointmentTime;

				// 遍历日期和时间列表，找到并禁用当前预约时间
				this.dateList.forEach((date, dateIndex) => {
					if (currentAppointmentTime.includes(date)) {
						// 找到日期匹配的项
						this.timeList[dateIndex].forEach((timeObj, timeIndex) => {
							// 检查时间是否匹配（不考虑"已满"标记）
							const timeText = timeObj.rawText || timeObj.text;
							if (currentAppointmentTime.includes(timeText)) {
								// 标记为已禁用，但使用不同的标记，表示"当前预约"
								this.timeList[dateIndex][timeIndex] = {
									...timeObj,
									text: `${timeText} (当前预约)`,
									disabled: true,
									isCurrentAppointment: true
								};
							}
						});
					}
				});
			}

			// 设置初始时间为第一个选项，不管是否可用
			if (this.dateList.length && this.timeList.length) {
				this.tempAppointmentTime = `${this.dateList[0]} ${this.timeList[0][0].text}`;
			}

			// 显示选择器
			this.showTimePicker = true;
		},

		// 获取时间选项
		async fetchTimeOptions() {
			try {
				const res = await getDateRange();
				if (res.data && Array.isArray(res.data)) {
					this.dateList = res.data.map(item => item.text);
					// 修改这里，添加已满标记
					this.timeList = res.data.map(item =>
						item.children.map(child => {
							// 返回带有文本和禁用状态的对象
							return {
								text: child.isFull ? `${child.text} (已满)` : child.text,
								disabled: child.isFull,
								rawText: child.text // 保存原始文本，用于提交
							};
						})
					);
				} else {
					this.setDefaultTimeOptions();
				}
			} catch (error) {
				console.error('获取预约时间选项失败:', error);
				uni.showToast({
					title: '获取预约时间失败',
					icon: 'none'
				});
				this.setDefaultTimeOptions();
			}
		},

		// 设置默认时间选项
		setDefaultTimeOptions() {
			const today = new Date();
			this.dateList = [
				this.formatDate(today),
				this.formatDate(new Date(today.setDate(today.getDate() + 1))),
				this.formatDate(new Date(today.setDate(today.getDate() + 1)))
			];

			const defaultTimes = ['9:00-10:00', '10:00-11:00', '14:00-15:00', '15:00-16:00'];
			this.timeList = Array(3).fill(defaultTimes);
		},

		// 格式化日期
		formatDate(date) {
			const year = date.getFullYear();
			const month = date.getMonth() + 1;
			const day = date.getDate();
			return `${year}年${month}月${day}日`;
		},

		// 处理时间选择
		handleTimeChange(e) {
			const values = e.detail.value;
			const dateIndex = values[0];
			const timeIndex = values[1];

			// 检查是否切换了日期
			if (this.currentDateIndex !== dateIndex) {
				// 日期变化，重置时间列到第一个位置
				this.currentDateIndex = dateIndex;
				this.pickerValue = [dateIndex, 0]; // 重置时间列到第一个位置

				// 直接使用第一个时间选项
				const selectedDate = this.dateList[dateIndex];
				const selectedTimeObj = this.timeList[dateIndex][0];
				const selectedTime = selectedTimeObj.rawText || selectedTimeObj.text;
				this.tempAppointmentTime = `${selectedDate} ${selectedTime}`;

				// 如果是当前预约时间，显示提示
				if (selectedTimeObj.isCurrentAppointment) {
					uni.showToast({
						title: '这是您当前的预约时间',
						icon: 'none'
					});
				}

				return; // 不继续执行，因为我们已经手动设置了值
			}

			// 如果只是时间变化，保持原有逻辑
			this.pickerValue = values;

			const selectedDate = this.dateList[dateIndex];
			const selectedTimeObj = this.timeList[dateIndex][timeIndex];

			// 检查所选时间段是否是当前预约
			if (selectedTimeObj.isCurrentAppointment) {
				uni.showToast({
					title: '不能选择当前预约时间',
					icon: 'none'
				});
				return;
			}

			// 使用原始文本（不带标记）
			const selectedTime = selectedTimeObj.rawText || selectedTimeObj.text;
			this.tempAppointmentTime = `${selectedDate} ${selectedTime}`;
		},

		// 处理列变化
		handleColumnChange(e) {
			const { column, value } = e.detail;
			if (column === 0) {
				this.currentDateIndex = value;
				this.pickerValue = [value, 0];
				this.tempAppointmentTime = `${this.dateList[value]} ${this.timeList[value][0]}`;
			}
		},

		// 关闭时间选择器
		closeTimePicker() {
			this.showTimePicker = false;
			this.tempAppointmentTime = '';
		},
		// 添加确认方法
		confirmTime() {
			if (!this.tempAppointmentTime) {
				uni.showToast({
					title: '请选择预约时间',
					icon: 'none'
				});
				return;
			}

			// 检查当前选择的时间是否是当前预约
			const dateIndex = this.pickerValue[0];
			const timeIndex = this.pickerValue[1];
			const selectedTimeObj = this.timeList[dateIndex][timeIndex];

			if (selectedTimeObj.isCurrentAppointment) {
				uni.showToast({
					title: '请选择与当前不同的预约时间',
					icon: 'none'
				});
				return;
			}

			// 获取当前订单
			const currentOrder = this.orderList.find(order => order.id === this.selectedOrderId);
			if (currentOrder && this.tempAppointmentTime === currentOrder.appointmentTime) {
				uni.showToast({
					title: '新预约时间与当前相同',
					icon: 'none'
				});
				return;
			}

			uni.showModal({
				title: '提示',
				content: `是否确认更改预约时间为\n ${this.tempAppointmentTime}？`,
				success: (res) => {
					if (res.confirm) {
						uni.showLoading({
							title: '更改中'
						});
						// 调用更改预约时间的API
						doChangeAppointmentTime({
							id: this.selectedOrderId,
							appointmentTime: this.tempAppointmentTime
						}).then(response => {
							uni.hideLoading();
							uni.showToast({
								title: '预约时间更改成功',
								icon: 'success'
							});
							this.loadData(); // 刷新订单列表
							this.showTimePicker = false; // 关闭选择器
						}).catch(error => {
							uni.hideLoading();
							uni.showToast({
								title: '更改失败，请重试',
								icon: 'none'
							});
						});
					}
				}
			});
		},
    // 判断预约时间是否为今天
    isAppointmentToday(appointmentTime) {
      if (!appointmentTime) return false;

      // 获取今天的日期
      const today = new Date();
      today.setHours(0, 0, 0, 0); // 去掉时间部分

      // 解析预约时间字符串
      const datePattern = /(\d{4})年(\d{1,2})月(\d{1,2})日/;
      const match = appointmentTime.match(datePattern);
      if (!match) return false;

      // 创建预约日期对象
      const appointmentDate = new Date(
          parseInt(match[1]), // 年
          parseInt(match[2]) - 1, // 月（0-based）
          parseInt(match[3]) // 日
      );

      // 比较日期
      return appointmentDate <= today;
    },

    // 处理日期变化
    handleDateChange(e) {
      console.log('Date changed:', e.detail.value);
      this.selectedDate = e.detail.value;

      // 设置筛选参数
      this.orderParam.appointmentDate = this.selectedDate;

      this.orderList = [];
      this.loadData('refresh');
    },
    // 格式化今天的日期为 YYYY-MM-DD 格式
    formatTodayDate() {
      const today = new Date();
      const year = today.getFullYear();
      const month = String(today.getMonth() + 1).padStart(2, '0');
      const day = String(today.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    // 添加触摸事件处理
    handleTouchStart(e) {
      this.startY = e.touches[0].pageY;
    },

    handleTouchMove(e) {
      const scrollView = e.currentTarget;
      const currentY = e.touches[0].pageY;
      const scrollTop = scrollView.scrollTop || 0;

      // 如果是向下滑动且已经在顶部，阻止事件冒泡
      if (currentY > this.startY && scrollTop <= 0) {
        e.stopPropagation();
        e.preventDefault();
      }
    },
	},
	onShow() {
		// 检查是否有保存的 tab 索引
		const savedTabIndex = uni.getStorageSync('orderListTabIndex');
		const savedSubTabIndex = uni.getStorageSync('orderListSubTabIndex');

		if (savedTabIndex !== '' && savedTabIndex !== null && savedTabIndex !== undefined) {
			// 恢复之前的 tab 索引
			this.tabCurrentIndex = parseInt(savedTabIndex);

			// 如果有子标签索引，也恢复它
			if (savedSubTabIndex !== '' && savedSubTabIndex !== null && savedSubTabIndex !== undefined) {
				const currentTab = this.filteredNavList[this.tabCurrentIndex];
				if (currentTab && currentTab.subTabs) {
					currentTab.currentSubTab = parseInt(savedSubTabIndex);

					// 设置筛选参数
					const subTab = currentTab.subTabs[currentTab.currentSubTab];
					this.orderParam.isHomePickup = subTab.isHomePickup;
				}
			}

			// 清除存储的索引
			uni.removeStorageSync('orderListTabIndex');
			uni.removeStorageSync('orderListSubTabIndex');
		}

    // 仅在首次进入页面时刷新数据
    if (!this.isInitialized) {
      this.loadData('refresh').catch(err => console.log('首次加载异常:', err));
      this.isInitialized = true;
    }
	},
  // 添加下拉刷新页面生命周期方法，增强微信小程序兼容性
  onPullDownRefresh() {
    this.onRefresh();
  },
  // 添加触底加载更多生命周期方法，增强微信小程序兼容性
  onReachBottom() {
    this.loadData('add').catch(err => console.log('触底加载异常:', err));
  },
}
</script>

<style lang="scss">
.head_bg {
	height: 200rpx;
	position: relative;
	/* 添加相对定位 */
	margin: 0 -20rpx;
	/* 抵消父容器的内边距 */
	width: calc(100% + 40rpx);
	/* 增加宽度以抵消内边距 */

	image {
		width: 100%;
		height: 100%;
	}

	.head_title {
    padding-top: 70rpx;
    position: absolute; /* 绝对定位 */
    top: 50%; /* 垂直居中 */
    left: 50%; /* 水平居中 */
    transform: translate(-50%, -50%); /* 精确居中 */
    color: #ffffff; /* 白色文字 */
    font-size: 36rpx; /* 字体大小 */
    z-index: 1; /* 确保文字在图片上层 */
	}
}

page,
.content {
	background: $page-color-base;
	height: 100%;
}

/* 添加下拉刷新的样式 */
.wx-pull-to-refresh,
.uni-scroll-view-refresher {
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.refresher-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.refresher-indicator {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid $base-color;
  border-top-color: transparent;
  animation: refresher-spin 1s linear infinite;
}

@keyframes refresher-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.swiper-box {
	height: calc(100% - 40px);
}

.list-scroll-content {
	height: 100%;
}

.navbar {
	display: flex;
	height: 40px;
	padding: 0 5px;
	background: #fff;
	box-shadow: 0 1px 5px rgba(0, 0, 0, .06);
	position: relative;
	z-index: 10;

	.nav-item {
		flex: 1;
		display: flex;
		justify-content: center;
		align-items: center;
		height: 100%;
		font-size: 15px;
		color: $font-color-dark;
		position: relative;

		&.current {
			color: $base-color;

			&:after {
				content: '';
				position: absolute;
				left: 50%;
				bottom: 0;
				transform: translateX(-50%);
				width: 44px;
				height: 0;
				border-bottom: 2px solid $base-color;
			}
		}
	}
}

/* 添加子标签栏样式 */
.sub-navbar {
  display: flex;
  height: 35px;
  padding: 0 5px;
  background: #f8f8f8;
  position: relative;
  z-index: 10;
  border-bottom: 1px solid #eee;

  .sub-nav-item {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    font-size: 14px;
    color: $font-color-dark;
    position: relative;

    &.current {
      color: $base-color;

      &:after {
        content: '';
        position: absolute;
        left: 50%;
        bottom: 0;
        transform: translateX(-50%);
        width: 40px;
        height: 0;
        border-bottom: 2px solid $base-color;
      }
    }
  }
}

/* 日期筛选样式 */
.date-filter {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1px solid #eee;
}

.date-filter-label {
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
  font-weight: 500;
}

.date-filter-btn {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  border: 1px solid #eee;
}

.date-filter-btn text {
  font-size: 28rpx;
  color: #333;
}

.date-filter-btn .iconfont {
  margin-left: 10rpx;
  font-size: 24rpx;
  color: #999;
}

/* 调整 swiper-box 高度，为子标签和日期筛选腾出空间 */
.swiper-box {
  height: calc(100% - 75px); /* 40px 主标签 + 35px 子标签 */
}

/* 调整 swiper-box 高度，为日期筛选腾出空间 */
.swiper-box.with-date-filter {
  height: calc(100% - 115px); /* 40px 主标签 + 35px 子标签 + 40px 日期筛选 */
}

.uni-swiper-item {
	height: auto;
}

.order-item {
	display: flex;
	flex-direction: column;
	padding-left: 30upx;
	background: #fff;
	margin: 16upx;
	border-radius: 15upx;

	.i-top {
		display: flex;
		align-items: center;
		height: 80upx;
		padding: 0 30upx; // 仅保留左右内边距
		margin-left: -30upx; // 抵消父容器的 padding-left
		width: calc(100% + 30upx); // 扩展宽度以覆盖父容器的 padding
		font-size: $font-base;
		color: $font-color-dark;
		border-radius: 8px 8px 0px 0px;
		background-color: rgba(0, 137, 255, 0.18); // 使用背景色的透明度
		box-shadow: 0px 2px 4px 0px rgba(152, 152, 152, 0.102);
		position: relative;

		.time {
			flex: 1;
		}

		.state {
			color: $font-color-dark;
			font-weight: 500;
		}
	}

	/* 单条商品 */
	.goods-box-single {
		display: flex;
		flex-direction: row;
		align-items: flex-start;
		margin: 0 30upx 20upx 0;

		.right {
			flex: 1;
			display: flex;
			flex-direction: column;
			padding: 16upx 0;
			overflow: hidden;
			margin-right: 8upx;

			view {
				display: flex;
				flex-direction: row;
				align-items: flex-start;
				padding: 5upx 0;
				border-bottom: none;
				justify-content: space-between;

				text:first-child {
					color: #666;
					font-size: 28upx;
					width: 140upx;
					flex-shrink: 0;
				}

				// 统一所有文本样式
				.title, .attr-box, .price, text:last-child {
					font-size: 28upx;
					color: #333;
					text-align: right;
					margin-right: 30upx;
				}
			}
		}
	}
  .order-upload-images {
    padding-left: 14rpx;
    margin: 0;
  }

	.action-box {
		display: flex;
		justify-content: flex-end;
		align-items: center;
		height: 100upx;
		position: relative;
		padding-right: 30upx;
	}

	.action-btn {
		width: 160upx;
		height: 60upx;
		margin: 0;
		margin-left: 24upx;
		padding: 0;
		text-align: center;
		line-height: 60upx;
		font-size: $font-sm + 2upx;
		color: $font-color-dark;
		background: #fff;
		border-radius: 100px;

		&:after {
			border-radius: 100px;
		}

    &.cancel {
      background: #E25D5D;
      color: #fff;
    }
		&.recom {
			background: #707070;
			color: #fff;

			&:after {
				border-color: #f7bcc8;
			}
		}
	}
}


/* load-more */
.uni-load-more {
	display: flex;
	flex-direction: row;
	height: 80upx;
	align-items: center;
	justify-content: center
}

.uni-load-more__text {
	font-size: 28upx;
	color: #999
}

.uni-load-more__img {
	height: 24px;
	width: 24px;
	margin-right: 10px
}

.uni-load-more__img>view {
	position: absolute
}

.uni-load-more__img>view view {
	width: 6px;
	height: 2px;
	border-top-left-radius: 1px;
	border-bottom-left-radius: 1px;
	background: #999;
	position: absolute;
	opacity: .2;
	transform-origin: 50%;
	animation: load 1.56s ease infinite
}

.uni-load-more__img>view view:nth-child(1) {
	transform: rotate(90deg);
	top: 2px;
	left: 9px
}

.uni-load-more__img>view view:nth-child(2) {
	transform: rotate(180deg);
	top: 11px;
	right: 0
}

.uni-load-more__img>view view:nth-child(3) {
	transform: rotate(270deg);
	bottom: 2px;
	left: 9px
}

.uni-load-more__img>view view:nth-child(4) {
	top: 11px;
	left: 0
}

.load1,
.load2,
.load3 {
	height: 24px;
	width: 24px
}

.load2 {
	transform: rotate(30deg)
}

.load3 {
	transform: rotate(60deg)
}

.load1 view:nth-child(1) {
	animation-delay: 0s
}

.load2 view:nth-child(1) {
	animation-delay: .13s
}

.load3 view:nth-child(1) {
	animation-delay: .26s
}

.load1 view:nth-child(2) {
	animation-delay: .39s
}

.load2 view:nth-child(2) {
	animation-delay: .52s
}

.load3 view:nth-child(2) {
	animation-delay: .65s
}

.load1 view:nth-child(3) {
	animation-delay: .78s
}

.load2 view:nth-child(3) {
	animation-delay: .91s
}

.load3 view:nth-child(3) {
	animation-delay: 1.04s
}

.load1 view:nth-child(4) {
	animation-delay: 1.17s
}

.load2 view:nth-child(4) {
	animation-delay: 1.3s
}

.load3 view:nth-child(4) {
	animation-delay: 1.43s
}

@-webkit-keyframes load {
	0% {
		opacity: 1
	}

	100% {
		opacity: .2
	}
}
</style>
<style lang="scss" scoped>
.picker-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;

  .mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
  }

  .picker-container {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;

    .picker-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx 30rpx;
      border-bottom: 1px solid #eee;

      .cancel, .confirm {
        color: #007AFF;
        font-size: 32rpx;
      }

      .title {
        font-size: 32rpx;
      }
    }

    .picker-view {
      width: 100%;
      height: 400rpx;

      .item {
        line-height: 80rpx;
        text-align: center;
      }
    }
  }
}
.countdown-container {
  display: flex;
  align-items: center;
  padding: 10rpx 30rpx;
  background-color: #FFF9F6;
  border-radius: 8rpx;
  margin: 10rpx 0;

  .countdown-label {
    font-size: 24rpx;
    color: #FF5722;
    margin-right: 10rpx;
  }
}
/* 底部空白间距 - 考虑安全区域 */
.bottom-gap {
  height: calc(140rpx + env(safe-area-inset-bottom));
  width: 100%;
}

.appointment-time {
  display: flex;
  flex-direction: column;
  align-items: flex-end;

  .time-detail {
    font-size: 24rpx;
    color: #666;
  }
}
</style>
