<template>
	<view class="upload-content">
		<block v-for="(item, index) in imageList" :key="index">
			<view class="upload-item">
				<image
					class="upload-img"
					:src="getImageSrc(item)"
					mode="aspectFill"
					@click="previewImage(index)"
					@error="onImageError(index, $event)"
					@load="onImageLoad(index, $event)"
					:lazy-load="false"
					:show-menu-by-longpress="false"
				></image>
				<!-- 添加加载状态显示 -->
				<view v-if="item.loading" class="upload-loading">
					<text>加载中...</text>
				</view>
				<!-- 添加错误状态显示 -->
				<view v-if="item.error" class="upload-error" @click="retryLoadImage(index)">
					<text>加载失败，点击重试</text>
				</view>
				<image
					v-if="!readonly"
					class="upload-del-btn"
					@click="delImage(index)"
					src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACYAAAAmCAYAAACoPemuAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyJpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNiAoV2luZG93cykiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6RjNBODgzQjUwNDM5MTFFOUJDMjlGN0UwRTJGMjVCNjQiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6RjNBODgzQjYwNDM5MTFFOUJDMjlGN0UwRTJGMjVCNjQiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpGM0E4ODNCMzA0MzkxMUU5QkMyOUY3RTBFMkYyNUI2NCIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpGM0E4ODNCNDA0MzkxMUU5QkMyOUY3RTBFMkYyNUI2NCIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PuYQTIAAAAHNSURBVHjazJgxasMwFIbjnsD4BDmCLxDQDZrNQ5bcILlBvXvpDRLIWmiHFkyH2t0LyVbwEkPpbE/eYlUCGYQax096UuIffkIgij7ryU/vyZuYy2e+Z56K70R8lsJcOfPn5AriMCvmZ2YKdMW8EQ/hRA9iEorwXlpZtObMRySQ6kysvrFWloHUEIcmUBuHUDLccmxQskErt7wyVLdy00tQ4Q2g5De2V9kNwWjffiNDA33f/42i6E13Qj5uNptlwJD+SyODuaqqqi/KVBTFOxSKA7VtW/NxSZI8AcbE2nuraZpvKgSBk6G4drsdBOwogz1CwAghH/JEl+BUKJ1VltPHHjoIAoeE4l53YFqb+RKcBSgqyqXhtxEKZwkKB3YOThYCqjPuCDoHZwGK3mGLtdPp5LmqTI1Dqe4p26EkNqA4iE6eg2z+iQ0okyQMATvYgLIIt9Y6knTyFBIudHqIq3BpmkL60VJ9O0sXZY8MBwSLtdNGEAQ/i8XiVXcz83EcEPDbuq/fzMdYWt+6GTmMsX2rh9q3TtsxNrzXhKt1rwg6rR1Dhdjqo7QMlWOvoWTF4imxQMRF7eaL8L5ohmwrLgDB8pCQc8DlcG7y538CDABJNGPqfaJgfgAAAABJRU5ErkJggg=="
					mode="scaleToFill">
				</image>
			</view>
		</block>
		<view
			class="upload-add-btn"
			v-if="rduLength > 0 && !readonly"
			@click="chooseImage"
		></view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			imageList: []
		};
	},
	props: {
		url: {
			type: String,
			value: '' //上传接口地址
		},
		count: {
			type: Number,
			value: 4 //单次可选择的图片数量
		},
		length: {
			type: Number,
			value: 50 //可上传总数量
		},
		initialImages: {  // 添加一个 props 来接收初始图片列表
			type: Array,
			default: () => []
		},
		readonly: {
			type: Boolean,
			default: false
		},
		// 添加文件类型限制属性
		fileExtname: {
			type: Array,
			default: () => ['jpg', 'jpeg', 'png']
		}
	},
	watch: {
		initialImages: {
			handler(newVal) {
				this.imageList = [...newVal];
			},
			immediate: true
		}
	},
	computed: {
		rduLength(){
			return this.length - this.imageList.length;
		}
	},
	methods: {
		//选择图片
		chooseImage: function(){
			uni.chooseImage({
				count: this.rduLength < this.count ? this.rduLength : this.count, //最多可以选择的图片张数，默认9
				sizeType: ['original', 'compressed'], //original 原图，compressed 压缩图，默认二者都有
				sourceType: ['album'], //album 从相册选图，camera 使用相机，默认二者都有
				success: async (res) => {
          const images = res.tempFilePaths;
          // 添加文件类型验证
          const validImages = this.validateFileTypes(res.tempFiles);
          if (validImages.length > 0) {
            try {
              // 压缩图片
              const compressedImages = await Promise.all(
                  validImages.map(path => this.compressImage(path))
              );
              this.uploadFiles(compressedImages);
            } catch (err) {
              console.error('压缩失败:', err);
              uni.showToast({
                title: '图片压缩失败',
                icon: 'none'
              });
            } finally {
              uni.hideLoading();
            }
          }
        }
			});
		},
    compressImage: function(filePath) {
        return new Promise((resolve, reject) => {
            // 获取图片信息
            uni.getImageInfo({
                src: filePath,
                success: (res) => {
                    // 设置最大尺寸
                    const MAX_WIDTH = res.width;
                    const MAX_HEIGHT = res.height;
                    let width = res.width;
                    let height = res.height;

                    if (width > height) {
                        if (width > MAX_WIDTH) {
                            height *= MAX_WIDTH / width;
                            width = MAX_WIDTH;
                        }
                    } else {
                        if (height > MAX_HEIGHT) {
                            width *= MAX_HEIGHT / height;
                            height = MAX_HEIGHT;
                        }
                    }

                    // 压缩图片
                    uni.compressImage({
                        src: filePath,
                        quality: 85,
                        width: width,
                        height: height,
                        success: (compressRes) => {
                            resolve(compressRes.tempFilePath);
                        },
                        fail: (err) => {
                            console.error('图片压缩失败:', err);
                            resolve(filePath); // 压缩失败返回原图
                        }
                    });
                },
                fail: (err) => {
                    console.error('获取图片信息失败:', err);
                    resolve(filePath); // 获取信息失败返回原图
                }
            });
        });
    },

		// 添加文件类型验证方法
		validateFileTypes(files) {
			const validFiles = [];
			const invalidFiles = [];

			// 创建扩展名到易读名称的映射
			const extToReadable = {
				'jpg': 'JPG',
				'jpeg': 'JPEG',
				'png': 'PNG',
				'gif': 'GIF',
				'bmp': 'BMP',
				'webp': 'WEBP',
				'pdf': 'PDF'
			};

			files.forEach(file => {
				console.log('文件信息:', file);
				// 通过文件扩展名推断文件类型
				const ext = file.path.split('.').pop().toLowerCase();
				if (this.fileExtname.includes(ext)) {
					validFiles.push(file.path);
				} else {
					console.log('文件类型不支持:', ext);
					// 获取易读的文件类型名称
					const readableType = extToReadable[ext] || ext.toUpperCase();
					invalidFiles.push(readableType);
				}
			});

			// 如果有无效文件，显示提示
			if (invalidFiles.length > 0) {
				// 去重，避免重复的类型
				const uniqueInvalidTypes = [...new Set(invalidFiles)];

				uni.showToast({
					title: `不支持${uniqueInvalidTypes.join('、')}格式，仅支持${this.fileExtname.map(ext => extToReadable[ext] || ext.toUpperCase()).join('、')}`,
					icon: 'none',
					duration: 3000
				});
			}

			return validFiles;
		},
		//上传图片
		async uploadFiles(images) {
			for (let i = 0; i < images.length; i++) {
				const newImage = {
					src: images[i],
					filePath: images[i]
				};

				try {
					const uploadResult = await this.uploadImage(images[i]);
					newImage.src = uploadResult;
					newImage.filePath = uploadResult;

					// 添加到图片列表
					this.imageList.push(newImage);

					// 通知父组件上传成功
					this.$emit('success', {
						code: 200,
						data: uploadResult
					});
				} catch (err) {
					console.error('Upload failed:', err);
					uni.showToast({
						title: '上传失败',
						icon: 'none'
					});
				}
			}
		},
		uploadImage: function(file) {
			return new Promise((resolve, reject)=> {
				const token = uni.getStorageSync('token');
				const formData = {
					thumb_mode: 1,
				};
				this.uploadTask = uni.uploadFile({
					url: this.url,
					filePath: file,
					name: 'file',
					header: {
						'Authorization': token
					},
					formData: formData,
					success(uploadFileResult) {
						const uploadFileRes = JSON.parse(uploadFileResult.data) || {};
						if(uploadFileRes.code === 200 && uploadFileRes.data) {
							// 确保返回图片URL
							resolve(uploadFileRes.data);
						} else {
							reject('接口返回错误');
						}
					},
					fail() {
						reject('网络链接错误');
					}
				});
			});
		},
		//删除图片
		delImage(index) {
			uni.showModal({
				content: '确定要放弃这张图片么？',
				success: (confirmRes) => {
					if (confirmRes.confirm) {
						const deletedImage = this.imageList[index];
						this.imageList.splice(index, 1);
						// 触发删除事件，通知父组件
						this.$emit('delete', {
							index,
							image: deletedImage
						});
					}
				}
			});
		},
		//预览图片
		previewImage: function(index){
			const urls = [];
			this.imageList.forEach((item)=> {
				urls.push(item.filePath);
			})
			uni.previewImage({
				current: urls[index],
				urls: urls,
				indicator: "number"
			})
		}
	}
}
</script>

<style lang="scss">
.upload-content {
  padding: 24rpx 28rpx;
  background-color: #fff;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30rpx;
}

.upload-item {
  position: relative;
  width: 100%;
  aspect-ratio: 1;

  .upload-img {
    width: 100%;
    height: 100%;
    border-radius: 8rpx;
    object-fit: cover;
  }

  .upload-del-btn {
    position: absolute;
    right: -16rpx;
    top: -14rpx;
    width: 36rpx;
    height: 36rpx;
    border: 4rpx solid #fff;
    border-radius: 100rpx;
  }

  .upload-progress {
    position: absolute;
    left: 0;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    color: #fff;
    font-size: 24rpx;
    border-radius: 8rpx;
  }
}

.upload-add-btn {
  position: relative;
  width: 100%;
  aspect-ratio: 1;
  border-radius: 8rpx;
  background: #f9f9f9;

  &:before,
  &:after {
    content: " ";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 4rpx;
    height: 60rpx;
    background-color: #d6d6d6;
  }

  &:after {
    width: 60rpx;
    height: 4rpx;
  }

  &:active {
    background-color: #f7f7f7;
  }
}
</style>
